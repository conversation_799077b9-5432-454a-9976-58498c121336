import os
import PyPDF2
import docx
from PIL import Image
import magic
import json
from typing import List, Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

class DocumentParser:
    """Service for parsing various document types and extracting content"""
    
    def __init__(self):
        self.allowed_extensions = {
            'pdf', 'doc', 'docx', 'txt', 'rtf',
            'png', 'jpg', 'jpeg', 'gif', 'bmp', 'tiff'
        }
        
    def allowed_file(self, filename: str) -> bool:
        """Check if file extension is allowed"""
        return '.' in filename and \
               filename.rsplit('.', 1)[1].lower() in self.allowed_extensions
    
    def get_file_type(self, filename: str) -> str:
        """Determine file type from extension"""
        if not filename or '.' not in filename:
            return 'unknown'
        
        ext = filename.rsplit('.', 1)[1].lower()
        
        if ext == 'pdf':
            return 'pdf'
        elif ext in ['doc', 'docx']:
            return 'word'
        elif ext in ['txt', 'rtf']:
            return 'text'
        elif ext in ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'tiff']:
            return 'image'
        else:
            return 'unknown'
    
    def extract_text_from_pdf(self, filepath: str) -> str:
        """Extract text content from PDF file"""
        try:
            text = ""
            with open(filepath, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
            return text.strip()
        except Exception as e:
            logger.error(f"Error extracting text from PDF {filepath}: {str(e)}")
            return ""
    
    def extract_text_from_docx(self, filepath: str) -> str:
        """Extract text content from DOCX file"""
        try:
            doc = docx.Document(filepath)
            text = ""
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
            return text.strip()
        except Exception as e:
            logger.error(f"Error extracting text from DOCX {filepath}: {str(e)}")
            return ""
    
    def extract_text_from_txt(self, filepath: str) -> str:
        """Extract text content from TXT file"""
        try:
            with open(filepath, 'r', encoding='utf-8') as file:
                return file.read().strip()
        except UnicodeDecodeError:
            # Try with different encoding
            try:
                with open(filepath, 'r', encoding='latin-1') as file:
                    return file.read().strip()
            except Exception as e:
                logger.error(f"Error extracting text from TXT {filepath}: {str(e)}")
                return ""
        except Exception as e:
            logger.error(f"Error extracting text from TXT {filepath}: {str(e)}")
            return ""
    
    def get_image_info(self, filepath: str) -> Dict[str, Any]:
        """Get image information and metadata"""
        try:
            with Image.open(filepath) as img:
                return {
                    'filename': os.path.basename(filepath),
                    'format': img.format,
                    'size': img.size,
                    'mode': img.mode,
                    'filepath': filepath
                }
        except Exception as e:
            logger.error(f"Error getting image info from {filepath}: {str(e)}")
            return {
                'filename': os.path.basename(filepath),
                'error': str(e),
                'filepath': filepath
            }
    
    def process_single_file(self, file_info: Dict[str, str]) -> Dict[str, Any]:
        """Process a single file and extract relevant content"""
        filepath = file_info['filepath']
        file_type = file_info['type']
        filename = file_info['filename']
        
        result = {
            'filename': filename,
            'type': file_type,
            'filepath': filepath,
            'content': '',
            'metadata': {}
        }
        
        try:
            if file_type == 'pdf':
                result['content'] = self.extract_text_from_pdf(filepath)
            elif file_type == 'word':
                result['content'] = self.extract_text_from_docx(filepath)
            elif file_type == 'text':
                result['content'] = self.extract_text_from_txt(filepath)
            elif file_type == 'image':
                result['metadata'] = self.get_image_info(filepath)
                result['content'] = f"Image file: {filename}"
            
            # Add file size
            result['metadata']['size'] = os.path.getsize(filepath)
            
        except Exception as e:
            logger.error(f"Error processing file {filepath}: {str(e)}")
            result['error'] = str(e)
        
        return result
    
    def process_files(self, uploaded_files: List[Dict[str, str]]) -> Dict[str, Any]:
        """Process all uploaded files and extract content"""
        processed_files = []
        all_text_content = []
        image_files = []
        
        for file_info in uploaded_files:
            processed_file = self.process_single_file(file_info)
            processed_files.append(processed_file)
            
            if processed_file['type'] in ['pdf', 'word', 'text'] and processed_file['content']:
                all_text_content.append({
                    'filename': processed_file['filename'],
                    'content': processed_file['content']
                })
            elif processed_file['type'] == 'image':
                image_files.append(processed_file)
        
        return {
            'processed_files': processed_files,
            'text_content': all_text_content,
            'image_files': image_files,
            'total_files': len(uploaded_files),
            'text_files_count': len(all_text_content),
            'image_files_count': len(image_files)
        }

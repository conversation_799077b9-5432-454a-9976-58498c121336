#!/usr/bin/env python3
"""
Setup script for the After Training Report Generator
"""

import os
import sys
import subprocess
import platform

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("ERROR: Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"✓ Python {sys.version.split()[0]} detected")
    return True

def install_dependencies():
    """Install required Python packages"""
    print("Installing dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"ERROR: Failed to install dependencies: {e}")
        return False

def create_directories():
    """Create necessary directories"""
    directories = ['uploads', 'reports']
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"✓ Created directory: {directory}")
        else:
            print(f"✓ Directory exists: {directory}")

def check_environment():
    """Check environment setup"""
    print("\nEnvironment Check:")
    print(f"Operating System: {platform.system()} {platform.release()}")
    print(f"Python executable: {sys.executable}")
    print(f"Working directory: {os.getcwd()}")
    
    # Check for environment variables
    if os.environ.get('GEMINI_API_KEY'):
        print("✓ GEMINI_API_KEY is set")
    else:
        print("⚠ GEMINI_API_KEY is not set")
        print("  You'll need to set this before running the application")

def main():
    """Main setup function"""
    print("=" * 60)
    print("After Training Report Generator - Setup")
    print("=" * 60)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        sys.exit(1)
    
    # Create directories
    create_directories()
    
    # Check environment
    check_environment()
    
    print("\n" + "=" * 60)
    print("Setup completed successfully!")
    print("=" * 60)
    print("\nNext steps:")
    print("1. Set your Google Gemini API key:")
    print("   export GEMINI_API_KEY='your-api-key-here'")
    print("   (or set it in your system environment variables)")
    print("\n2. Run the application:")
    print("   python run.py")
    print("\n3. Open your browser to:")
    print("   http://localhost:5000")
    print("=" * 60)

if __name__ == '__main__':
    main()

{% extends "base.html" %}

{% block title %}Preview Report - Report Generator{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-eye me-2"></i>
                Preview & Edit Report Data
            </h2>
            <div>
                <button type="button" class="btn btn-success btn-lg" id="generateBtn">
                    <i class="fas fa-file-pdf me-2"></i>
                    Generate Final Report
                </button>
                <a href="{{ url_for('index') }}" class="btn btn-secondary btn-lg ms-2">
                    <i class="fas fa-arrow-left me-2"></i>
                    Start Over
                </a>
            </div>
        </div>

        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            Review the extracted information below. You can edit any field before generating the final report.
        </div>

        <form id="previewForm">
            <!-- Training Details Section -->
            <div class="form-section">
                <h4 class="section-title">I. Training Details</h4>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">Course Title</label>
                            <input type="text" class="form-control" name="training_details.course_title" 
                                   value="{{ structured_data.training_details.course_title }}">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">Course Code</label>
                            <input type="text" class="form-control" name="training_details.course_code" 
                                   value="{{ structured_data.training_details.course_code }}">
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label fw-bold">Date</label>
                            <input type="date" class="form-control" name="training_details.date" 
                                   value="{{ structured_data.training_details.date }}">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label fw-bold">Time</label>
                            <input type="text" class="form-control" name="training_details.time" 
                                   value="{{ structured_data.training_details.time }}" placeholder="e.g., 9:00 AM - 5:00 PM">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label fw-bold">Duration</label>
                            <input type="text" class="form-control" name="training_details.duration" 
                                   value="{{ structured_data.training_details.duration }}" placeholder="e.g., 8 hours">
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">Venue</label>
                            <input type="text" class="form-control" name="training_details.venue" 
                                   value="{{ structured_data.training_details.venue }}">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">Resource Person</label>
                            <input type="text" class="form-control" name="training_details.resource_person" 
                                   value="{{ structured_data.training_details.resource_person }}">
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label fw-bold">Platform Used</label>
                            <input type="text" class="form-control" name="training_details.platform_used" 
                                   value="{{ structured_data.training_details.platform_used }}" placeholder="e.g., Zoom, Teams">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label fw-bold">Mode</label>
                            <select class="form-control" name="training_details.mode">
                                <option value="Online" {{ 'selected' if structured_data.training_details.mode == 'Online' }}>Online</option>
                                <option value="Face-to-face" {{ 'selected' if structured_data.training_details.mode == 'Face-to-face' }}>Face-to-face</option>
                                <option value="Hybrid" {{ 'selected' if structured_data.training_details.mode == 'Hybrid' }}>Hybrid</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label fw-bold">Target Participants</label>
                            <input type="text" class="form-control" name="training_details.target_participants" 
                                   value="{{ structured_data.training_details.target_participants }}">
                        </div>
                    </div>
                </div>
                
                <!-- Attendees Section -->
                <h6 class="mt-4 mb-3">Attendees Information</h6>
                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label fw-bold">Total Attendees</label>
                            <input type="number" class="form-control" name="training_details.total_attendees" 
                                   value="{{ structured_data.training_details.total_attendees }}">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label fw-bold">Male Attendees</label>
                            <input type="number" class="form-control" name="training_details.attendees_male" 
                                   value="{{ structured_data.training_details.attendees_male }}">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label fw-bold">Female Attendees</label>
                            <input type="number" class="form-control" name="training_details.attendees_female" 
                                   value="{{ structured_data.training_details.attendees_female }}">
                        </div>
                    </div>
                </div>
                
                <!-- Sector Categories -->
                <h6 class="mt-4 mb-3">Sector Categories</h6>
                <div class="row">
                    <div class="col-md-3">
                        <label class="form-label fw-bold">NGA</label>
                        <div class="row">
                            <div class="col-6">
                                <input type="number" class="form-control form-control-sm" 
                                       name="training_details.sector_categories.nga.male" 
                                       value="{{ structured_data.training_details.sector_categories.nga.male }}" 
                                       placeholder="Male">
                            </div>
                            <div class="col-6">
                                <input type="number" class="form-control form-control-sm" 
                                       name="training_details.sector_categories.nga.female" 
                                       value="{{ structured_data.training_details.sector_categories.nga.female }}" 
                                       placeholder="Female">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label fw-bold">LGU</label>
                        <div class="row">
                            <div class="col-6">
                                <input type="number" class="form-control form-control-sm" 
                                       name="training_details.sector_categories.lgu.male" 
                                       value="{{ structured_data.training_details.sector_categories.lgu.male }}" 
                                       placeholder="Male">
                            </div>
                            <div class="col-6">
                                <input type="number" class="form-control form-control-sm" 
                                       name="training_details.sector_categories.lgu.female" 
                                       value="{{ structured_data.training_details.sector_categories.lgu.female }}" 
                                       placeholder="Female">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label fw-bold">SUC</label>
                        <div class="row">
                            <div class="col-6">
                                <input type="number" class="form-control form-control-sm" 
                                       name="training_details.sector_categories.suc.male" 
                                       value="{{ structured_data.training_details.sector_categories.suc.male }}" 
                                       placeholder="Male">
                            </div>
                            <div class="col-6">
                                <input type="number" class="form-control form-control-sm" 
                                       name="training_details.sector_categories.suc.female" 
                                       value="{{ structured_data.training_details.sector_categories.suc.female }}" 
                                       placeholder="Female">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label fw-bold">Others</label>
                        <div class="row">
                            <div class="col-6">
                                <input type="number" class="form-control form-control-sm" 
                                       name="training_details.sector_categories.others.male" 
                                       value="{{ structured_data.training_details.sector_categories.others.male }}" 
                                       placeholder="Male">
                            </div>
                            <div class="col-6">
                                <input type="number" class="form-control form-control-sm" 
                                       name="training_details.sector_categories.others.female" 
                                       value="{{ structured_data.training_details.sector_categories.others.female }}" 
                                       placeholder="Female">
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Certificates -->
                <h6 class="mt-4 mb-3">Certificates Issued</h6>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">Male Certificates</label>
                            <input type="number" class="form-control" name="training_details.total_certificates.male" 
                                   value="{{ structured_data.training_details.total_certificates.male }}">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">Female Certificates</label>
                            <input type="number" class="form-control" name="training_details.total_certificates.female" 
                                   value="{{ structured_data.training_details.total_certificates.female }}">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Other Sections -->
            <div class="form-section">
                <h4 class="section-title">II. Rationale</h4>
                <textarea class="form-control" name="rationale" rows="4" 
                          placeholder="Purpose and justification for the training">{{ structured_data.rationale }}</textarea>
            </div>

            <div class="form-section">
                <h4 class="section-title">III. Objectives</h4>
                <textarea class="form-control" name="objectives" rows="4" 
                          placeholder="Training objectives and learning outcomes">{{ structured_data.objectives }}</textarea>
            </div>

            <div class="form-section">
                <h4 class="section-title">IV. Topics Covered</h4>
                <textarea class="form-control" name="topics_covered" rows="5" 
                          placeholder="List of topics and modules covered during training">{{ structured_data.topics_covered }}</textarea>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-section">
                        <h4 class="section-title">V. Issues and Concerns</h4>
                        <textarea class="form-control" name="issues_and_concerns" rows="6" 
                                  placeholder="Any issues or concerns identified during training">{{ structured_data.issues_and_concerns }}</textarea>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-section">
                        <h4 class="section-title">VI. Recommendations</h4>
                        <textarea class="form-control" name="recommendations" rows="6" 
                                  placeholder="Recommendations for improvement">{{ structured_data.recommendations }}</textarea>
                    </div>
                </div>
            </div>

            <div class="form-section">
                <h4 class="section-title">VII. Plans and Action Items</h4>
                <textarea class="form-control" name="plans_and_action_items" rows="4" 
                          placeholder="Next steps and action items">{{ structured_data.plans_and_action_items }}</textarea>
            </div>

            <!-- Signature Section -->
            <div class="form-section">
                <h4 class="section-title">Signatures</h4>
                <div class="row">
                    <div class="col-md-6">
                        <h6>Prepared by:</h6>
                        <div class="mb-3">
                            <label class="form-label">Name</label>
                            <input type="text" class="form-control" name="prepared_by.name" 
                                   value="{{ structured_data.prepared_by.name }}">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Position</label>
                            <input type="text" class="form-control" name="prepared_by.position" 
                                   value="{{ structured_data.prepared_by.position }}">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>Noted by:</h6>
                        <div class="mb-3">
                            <label class="form-label">Name</label>
                            <input type="text" class="form-control" name="noted_by.name" 
                                   value="{{ structured_data.noted_by.name }}">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Position</label>
                            <input type="text" class="form-control" name="noted_by.position" 
                                   value="{{ structured_data.noted_by.position }}">
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center mt-4">
                <button type="button" class="btn btn-primary btn-lg me-3" id="saveChangesBtn">
                    <i class="fas fa-save me-2"></i>
                    Save Changes
                </button>
                <button type="button" class="btn btn-success btn-lg" id="generateBtn2">
                    <i class="fas fa-file-pdf me-2"></i>
                    Generate Final Report
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
const sessionId = '{{ session_data.session_id }}';

// Save changes functionality
document.getElementById('saveChangesBtn').addEventListener('click', function() {
    const formData = new FormData(document.getElementById('previewForm'));
    const data = {};
    
    // Convert FormData to nested object
    for (let [key, value] of formData.entries()) {
        setNestedValue(data, key, value);
    }
    
    fetch(`/update_data/${sessionId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showAlert('Changes saved successfully!', 'success');
        } else {
            showAlert('Error saving changes: ' + result.error, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('Error saving changes: ' + error.message, 'danger');
    });
});

// Generate report functionality
function generateReport() {
    // Save changes first, then generate
    const formData = new FormData(document.getElementById('previewForm'));
    const data = {};
    
    for (let [key, value] of formData.entries()) {
        setNestedValue(data, key, value);
    }
    
    fetch(`/update_data/${sessionId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            // Redirect to generate report
            window.location.href = `/generate_report/${sessionId}`;
        } else {
            showAlert('Error saving changes: ' + result.error, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('Error: ' + error.message, 'danger');
    });
}

document.getElementById('generateBtn').addEventListener('click', generateReport);
document.getElementById('generateBtn2').addEventListener('click', generateReport);

// Helper function to set nested object values
function setNestedValue(obj, path, value) {
    const keys = path.split('.');
    let current = obj;
    
    for (let i = 0; i < keys.length - 1; i++) {
        const key = keys[i];
        if (!(key in current)) {
            current[key] = {};
        }
        current = current[key];
    }
    
    current[keys[keys.length - 1]] = value;
}

// Helper function to show alerts
function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.querySelector('.container').insertBefore(alertDiv, document.querySelector('.row'));
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
{% endblock %}

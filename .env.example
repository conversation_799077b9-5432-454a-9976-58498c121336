# Environment Variables for After Training Report Generator

# Google Gemini API Configuration
GEMINI_API_KEY=AIzaSyA1ZMcIkXVRC-N0U22QR425BoxhuVWWGbY

# Flask Configuration
SECRET_KEY=your-secret-key-for-production
FLASK_ENV=development

# Application Settings
LOG_LEVEL=INFO

# File Upload Settings (optional - defaults are set in config.py)
# MAX_CONTENT_LENGTH=52428800  # 50MB in bytes
# UPLOAD_FOLDER=./uploads
# REPORTS_FOLDER=./reports

# Instructions:
# 1. Copy this file to .env
# 2. Replace the placeholder values with your actual configuration
# 3. Never commit the .env file to version control

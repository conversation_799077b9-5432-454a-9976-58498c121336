from flask import Flask, render_template, request, jsonify, send_file, flash, redirect, url_for
from werkzeug.utils import secure_filename
import os
import json
from datetime import datetime
import uuid
from services.document_parser import DocumentParser
from services.gemini_service import GeminiService
from services.report_generator import ReportGenerator
from config import Config

app = Flask(__name__)
app.config.from_object(Config)

# Initialize services
document_parser = DocumentParser()
gemini_service = GeminiService()
report_generator = ReportGenerator()

# Ensure upload directory exists
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs(app.config['REPORTS_FOLDER'], exist_ok=True)

@app.route('/')
def index():
    """Main upload interface"""
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_files():
    """Handle file uploads and process documents"""
    try:
        if 'files' not in request.files:
            return jsonify({'error': 'No files uploaded'}), 400
        
        files = request.files.getlist('files')
        if not files or all(f.filename == '' for f in files):
            return jsonify({'error': 'No files selected'}), 400
        
        session_id = str(uuid.uuid4())
        session_folder = os.path.join(app.config['UPLOAD_FOLDER'], session_id)
        os.makedirs(session_folder, exist_ok=True)
        
        uploaded_files = []
        for file in files:
            if file and file.filename:
                if document_parser.allowed_file(file.filename):
                    filename = secure_filename(file.filename)
                    filepath = os.path.join(session_folder, filename)
                    file.save(filepath)
                    uploaded_files.append({
                        'filename': filename,
                        'filepath': filepath,
                        'type': document_parser.get_file_type(filename)
                    })
        
        if not uploaded_files:
            return jsonify({'error': 'No valid files uploaded'}), 400
        
        # Process files and extract data
        extracted_data = document_parser.process_files(uploaded_files)
        
        # Use Gemini to enhance and structure the data
        structured_data = gemini_service.structure_training_data(extracted_data)
        
        # Save session data
        session_data = {
            'session_id': session_id,
            'uploaded_files': uploaded_files,
            'extracted_data': extracted_data,
            'structured_data': structured_data,
            'created_at': datetime.now().isoformat()
        }
        
        session_file = os.path.join(session_folder, 'session_data.json')
        with open(session_file, 'w', encoding='utf-8') as f:
            json.dump(session_data, f, ensure_ascii=False, indent=2)
        
        return jsonify({
            'success': True,
            'session_id': session_id,
            'structured_data': structured_data,
            'files_processed': len(uploaded_files)
        })
        
    except Exception as e:
        app.logger.error(f"Upload error: {str(e)}")
        return jsonify({'error': f'Processing failed: {str(e)}'}), 500

@app.route('/preview/<session_id>')
def preview_report(session_id):
    """Preview extracted data before generating report"""
    try:
        session_folder = os.path.join(app.config['UPLOAD_FOLDER'], session_id)
        session_file = os.path.join(session_folder, 'session_data.json')
        
        if not os.path.exists(session_file):
            flash('Session not found', 'error')
            return redirect(url_for('index'))
        
        with open(session_file, 'r', encoding='utf-8') as f:
            session_data = json.load(f)
        
        return render_template('preview.html', 
                             session_data=session_data,
                             structured_data=session_data['structured_data'])
        
    except Exception as e:
        app.logger.error(f"Preview error: {str(e)}")
        flash(f'Error loading preview: {str(e)}', 'error')
        return redirect(url_for('index'))

@app.route('/update_data/<session_id>', methods=['POST'])
def update_data(session_id):
    """Update extracted data based on user edits"""
    try:
        session_folder = os.path.join(app.config['UPLOAD_FOLDER'], session_id)
        session_file = os.path.join(session_folder, 'session_data.json')
        
        if not os.path.exists(session_file):
            return jsonify({'error': 'Session not found'}), 404
        
        # Load existing session data
        with open(session_file, 'r', encoding='utf-8') as f:
            session_data = json.load(f)
        
        # Update with user modifications
        updated_data = request.json
        session_data['structured_data'].update(updated_data)
        session_data['modified_at'] = datetime.now().isoformat()
        
        # Save updated session data
        with open(session_file, 'w', encoding='utf-8') as f:
            json.dump(session_data, f, ensure_ascii=False, indent=2)
        
        return jsonify({'success': True, 'message': 'Data updated successfully'})
        
    except Exception as e:
        app.logger.error(f"Update error: {str(e)}")
        return jsonify({'error': f'Update failed: {str(e)}'}), 500

@app.route('/generate_report/<session_id>')
def generate_report(session_id):
    """Generate final report"""
    try:
        session_folder = os.path.join(app.config['UPLOAD_FOLDER'], session_id)
        session_file = os.path.join(session_folder, 'session_data.json')
        
        if not os.path.exists(session_file):
            flash('Session not found', 'error')
            return redirect(url_for('index'))
        
        with open(session_file, 'r', encoding='utf-8') as f:
            session_data = json.load(f)
        
        # Generate report
        report_path = report_generator.generate_report(
            session_data['structured_data'], 
            session_id,
            app.config['REPORTS_FOLDER']
        )
        
        return send_file(report_path, as_attachment=True, 
                        download_name=f'after_training_report_{session_id}.pdf')
        
    except Exception as e:
        app.logger.error(f"Report generation error: {str(e)}")
        flash(f'Error generating report: {str(e)}', 'error')
        return redirect(url_for('preview_report', session_id=session_id))

@app.route('/health')
def health_check():
    """Health check endpoint"""
    return jsonify({'status': 'healthy', 'timestamp': datetime.now().isoformat()})

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)

{"session_id": "71098681-69be-446f-9665-77827379506b", "uploaded_files": [{"filename": "INFO_SESSION__App_Development_for_Startupssigned_1.pdf", "filepath": "C:\\Users\\<USER>\\Documents\\report_generator\\uploads\\71098681-69be-446f-9665-77827379506b\\INFO_SESSION__App_Development_for_Startupssigned_1.pdf", "type": "pdf"}, {"filename": "Post_Activity_Report-Infosession-June_2_2025.pdf", "filepath": "C:\\Users\\<USER>\\Documents\\report_generator\\uploads\\71098681-69be-446f-9665-77827379506b\\Post_Activity_Report-Infosession-June_2_2025.pdf", "type": "pdf"}], "extracted_data": {"processed_files": [{"filename": "INFO_SESSION__App_Development_for_Startupssigned_1.pdf", "type": "pdf", "filepath": "C:\\Users\\<USER>\\Documents\\report_generator\\uploads\\71098681-69be-446f-9665-77827379506b\\INFO_SESSION__App_Development_for_Startupssigned_1.pdf", "content": "ACTIVITY DESIGN  \n Name of Activity : Online Info Session on Introduction to Application Development for    Startups Date   : June 2, 2025 Time   : 1:00PM - 5:00PM Venue   : Online via zoom Target Participants : 100 participants from LGUs, NGA, SUCs, and Residents in Surigao del Sur  Rationale/Background of the Activity  As mandated by Republic Act No. 10844, the Department of Information and Communications \nTechnology\n \n(DICT)\n \nis\n \ndirected\n \nto\n \nbe\n \nthe\n \nprimary\n \npolicy,\n \nplanning,\n \ncoordinating,\n \nimplementing,\n \nand\n \nadministrative\n \nentity\n \nof\n \nthe\n \nExecutive\n \nBranch\n \nof\n \nthe\n \ngovernment\n \nthat\n \nwill\n \nplan,\n \ndevelop,\n \nand\n \npromote\n \nthe\n \nnational\n \nICT\n \ndevelopment\n \nagenda.\n \nAmong\n \nothers,\n \nDICT\n \nis\n \nexpected\n \nto\n \nstrengthen\n \nits\n \neﬀorts\n \nin\n \nthe\n \nnation's\n \ncapacity\n \nbuilding\n \nthrough\n \nICT.\n  In line with this mandate, this training aims to equip aspiring developers, startups, and \nentrepreneurs\n \nwith\n \nessential\n \nknowledge\n \nand\n \nskills\n \nin\n \nmobile\n \napp\n \ndevelopment.\n \nBy\n \nintroducing\n \nparticipants\n \nto\n \nmodern\n \ntools\n \nlike\n \nthe\n \nIonic\n \nFramework\n \nand\n \nguiding\n \nthem\n \nthrough\n \nthe\n \napp\n \ndevelopment\n \nprocess—from\n \nsetup\n \nto\n \ndeployment—this\n \nsession\n \ncontributes\n \nto\n \nfostering\n \ninnovation,\n \ndigital\n \nentrepreneurship,\n \nand\n \nICT\n \nempowerment\n \nin\n \nthe\n \nPhilippine.\n  Objectives  At the end of this program, participants will be able to:  \n●\n \nUnderstand\n \nthe\n \nkey\n \nsteps\n \nin\n \ndeveloping\n \na\n \nmobile\n \napp\n \nusing\n \nthe\n \nIonic\n \nFramework.\n \n●\n \nIdentify\n \nthe\n \ntools\n \nand\n \nenvironment\n \nneeded\n \nto\n \ncreate\n \nand\n \ndeploy\n \na\n \ncross-platform\n \nmobile\n \napp.\n \n●\n \nObserve\n \nthe\n \nprocess\n \nof\n \ncreating\n \nan\n \nIonic\n \napp,\n \nadding\n \npages,\n \nand\n \nconnecting\n \nto\n \na\n \nbackend\n \nserver.\n \n●\n \nRecognize\n \nthe\n \nrequirements\n \nfor\n \ndeploying\n \nan\n \napp\n \nbackend\n \nonline\n \nand\n \npublishing\n \nan\n \nAndroid\n \napp\n \non\n \nthe\n \nGoogle\n \nPlay\n \nStore\n  Expected Output \nBy the end of this program, participants are expected to: \n●\n \nDemonstrate\n \na\n \nclear\n \nunderstanding\n \nof\n \nthe\n \nmobile\n \napp\n \ndevelopment\n \nprocess\n \nusing\n \nthe\n \nIonic\n \nFramework,\n \nincluding\n \nproject\n \nsetup\n \nand\n \nworkﬂow.\n \n●\n \nIdentify\n \nand\n \nconﬁgure\n \nthe\n \nnecessary\n \ntools\n \nand\n \ndevelopment\n \nenvironment\n \nfor\n \nbuilding\n \nand\n \ndeploying\n \ncross-platform\n \nmobile\n \napplications.\n \n \n●\n \nFollow\n \nand\n \nreplicate\n \nthe\n \ncreation\n \nof\n \nan\n \nIonic\n \napp\n,\n \nincluding\n \nadding\n \nfunctional\n \npages\n \nand\n \nestablishing\n \na\n \nconnection\n \nto\n \na\n \nbackend\n \nserver.\n \n  \n VERSION DATE  REVISED BY  CHANGES MADE \nv1.0 05/19/2025 Engr. Elizalde S. Ramos, Jr. Initial draft \n\n       Methodology  The program combines online sessions to deliver a strong learning experience. It integrates lectures, \ngroup\n \ndiscussions,\n \nand\n \nreal-world\n \ndemonstration\n \nto\n \nensure\n \ncomprehensive\n \nunderstanding\n \nand\n \npractical\n \napplication.\n  Duration  \nMode of Delivery No. of sessions  No. of hours / session (day) Total no. of Hours \nOnline 1 4 4 \n Pre-requisites  \n●\n \nMust\n \nhave\n \na\n \npersonal\n \ncomputer\n \nwith\n \nreliable\n \ninternet\n \nconnection.\n \n \n●\n \nWilling\n \nto\n \nﬁnish\n \nthe\n \nwhole\n \nduration\n \nof\n \nthe\n \nsession.\n \n \n●\n \nAims\n \nto\n \nenhance\n \ntheir\n \nskills\n \nin\n \nthe\n \nﬁeld\n \nof\n \nICT.\n \n  Targer Participants   100 participants from LGUs, NGA, SUCs, and Residents in Surigao del Sur   Indicative Program Flow  \nDetailed Course Outline \nModule Number Topic/s or Module/s: Instructional Material/s Exercises/ Assessments \nModule 1  (Time : 10–15 mins)   Introduction to App Development for Startup\n PowerPoint (overview of app development process, tools used) Short Q&A:  Why is app development important for startups? \nModule 2  (Time :35–40 mins )   Overview of Ionic Framework & Environment Setup Learning Module on Mobile Applications; PowerPoint; Live Code Walkthrough; VS Code, JDK, Android Studio Interactive poll:  Which platforms do you plan to deploy on?\n \nModule 3 (Time :20–25 mins ) Creating a New Ionic App (basic structure) Learning Module on Mobile Applications; Terminal/CLI Demo; Sample Code Snippets Mini activity: Guide them to list app components/features they plan to build\n \n \n\n      \nModule 4  (Time : 25–30 mins)  Adding App Pages & Navigation Learning Module on Mobile Applications; Code Examples (HTML, Ionic Components); VS Code Screenshots Reﬂective question: What pages will your startup app need? \nModule 5  (Time : 35–40 mins)  Connecting to Backend API (PHP + MySQL) Learning Module on Mobile Applications; XAMPP (Localhost Server); API Code & Database Group sharing:  What data will your app need from a backend? \nModule 6 (Time : 35–40 mins)  Deploying App Backend to DigitalOcean & Domain Linking Learning Module on Mobile Applications; FTP (FileZilla); Domain Name (GoDaddy); Live Server (DigitalOcean) Short quiz:  List 2 requirements for deploying an app backend online \nModule 7  (Time : 20–25 min)  Building Android App & Testing with Android Studio Learning Module on Mobile Applications; Android Studio for APK; Mobile Phone Open discussion: What is required to publish an app on Google Play Store? \nModule 8  (Time : 10–15 mins)  Overview of Google Play Store Publishing Process Learning Module on Mobile Applications; PowerPoint of Play Console Steps & Requirements Checklist Q&A:  What app details are required in a store listing? \nModule 9  (Time : 20–25 mins)  Wrap-Up, Open Forum, and Certiﬁcate Distribution Feedback form link; summary slide Completion acknowledgment; submission of feedback form \n Resource Requirements  A. Manpower  ● Course/Seminar Oﬃcer ● Resource Person. ● Facilities Assistant / Technical Support ● Administrative Assistant  B. Facilities/Equipment Training Room / Computer laboratory equipped with the following:  ● Laptops / desktops ● Adequate PA system ● Stable internet connection ● Large screen or LCD projector ● Whiteboard ● CCTV Camera \n \n\n       C. Training Materials  ● Training Schedule via Zoom  ● Presentation Materials ● Training Kit    Budget Estimate:   \nExpense Item  Particulars  Amount \nRPs Honoraria 4 hours virtual training * 1,400 per hour = 5,600.00 5,600.00 \nTraining Supplies  A4 Bond Paper *  5 reams * 280 = 1,400.00 Expanded Envelope * 20pcs * 30 =  600.00 Ballpoint pen * 2 box * 200  = 400.00 Non-permanent marker * 2pcs * 150 = 300.00  2,700.00 \nTOTAL  8,300.00 \n \nApproved by:          MARIO P. CUÑADO           Regional Director  \n Prepared by:    ENGR. ELIZALDE S. RAMOS, JR. Provincial Oﬃcer, SDS Concurred by:    ENGR. ALBERT C. GABRIEL  ITO II -  ILCDB Project Focal  \n  \nRecommending Approval:    ENGR. LAWRENCE P. SALANG Chief, Technical Operations Division   Certiﬁed Funds Available:    MARK CHRISTIAN L. ARIZALA Budget Oﬃcer II", "metadata": {"size": 1783652}}, {"filename": "Post_Activity_Report-Infosession-June_2_2025.pdf", "type": "pdf", "filepath": "C:\\Users\\<USER>\\Documents\\report_generator\\uploads\\71098681-69be-446f-9665-77827379506b\\Post_Activity_Report-Infosession-June_2_2025.pdf", "content": "○\n \n \nPOST-ACTIVITY REPORT   Activity Name:  Information Sessions on the following topics: \n●\n \nIntroduction\n \nto\n \nUser\n \nInterface\n \n(UI)\n \n&\n \nUser\n \nExperience\n \n(UX)\n \nDesign\n \n●\n \nIntroduction\n \nto\n \nApplication\n \nDevelopment\n \nfor\n \nStartups\n Venue: Zoom Date: June 02, 2025 Time: 8:00 AM - 5:00 PM  Activity Highlights \n●\n \nM orning\n \nSession\n \n(Introduction\n \nto\n \nUser\n \nInterface\n \n(UI)\n \n&\n \nUser\n \nExperience\n \n(UX)\n \nDesign): \n○\n \nParticipants\n \nwere\n \nintroduced\n \nto\n \nthe\n \nfundamental\n \nconcepts\n \nof\n \nUser\n \nInterface\n \n(UI)\n \nand\n \nUser\n \nExperience\n \n(UX)\n \ndesign. \n○\n \nKey\n \nprinciples,\n \nhistorical\n \ncontext,\n \nand\n \nthe\n \nimportance\n \nof\n \nUI/UX\n \nin\n \nmodern\n \napplications\n \nwere\n \ndiscussed. \n○\n \nA\n \npractical\n \ndemonstration\n \nwas\n \nconducted\n \non\n \ncreating\n \napplication\n \ninterfaces\n \nusing\n \nFigma,\n \ncovering\n \nbasic\n \ntools\n \nand\n \nworkﬂows. \n○\n \nTopics\n \nincluded\n \nthe\n \ndiﬀerences\n \nbetween\n \nUI\n \nand\n \nUX,\n \nwhy\n \nUI/UX\n \nis\n \nimportant,\n \ngood\n \nvs.\n \nbad\n \ndesign\n \nexamples,\n \nand\n \nkey\n \nprinciples\n \nof\n \nUX\n \ndesign.\n  \n●\n \nAfternoon\n \nSession\n \n(Introduction\n \nto\n \nApplication\n \nDevelopment\n \nfor\n \nStartups): \n○\n \nFocused\n \non\n \nequipping\n \naspiring\n \ndevelopers,\n \nstartups,\n \nand\n \nentrepreneurs\n \nwith\n \nessential\n \nknowledge\n \nand\n \nskills\n \nin\n \nmobile\n \napp\n \ndevelopment. \n○\n \nIntroduction\n \nto\n \nthe\n \nIonic\n \nFramework\n \nfor\n \ncross-platform\n \nmobile\n \napp\n \ndevelopment. \n○\n \nCovered\n \nthe\n \napp\n \ndevelopment\n \nlifecycle\n \nfrom\n \nsetup\n \nto\n \ndeployment,\n \nincluding: \n■\n \nOverview\n \nof\n \nIonic\n \nFramework\n \nand\n \nenvironment\n \nsetup\n \n(VS\n \nCode,\n \nJDK,\n \nAndroid\n \nStudio). \n■\n \nCreating\n \na\n \nnew\n \nIonic\n \napp\n \n(basic\n \nstructure,\n \nTerminal/CLI\n \ndemo). \n■\n \nAdding\n \napp\n \npages,\n \nnavigation,\n \nand\n \nconnecting\n \nto\n \na\n \nbackend\n \nAPI\n \n(PHP\n \n+\n \nMySQL,\n \nXAMPP). \n■\n \nDeploying\n \nan\n \napp\n \nbackend\n \n(FTP/FileZilla,\n \ndomain\n \nname,\n \nDigitalOcean). \n■\n \nBuilding\n \nand\n \ntesting\n \nan\n \nAndroid\n \nApp\n \n(Android\n \nStudio\n \nfor\n \nAPK). \n○\n \nIncluded\n \ndiscussions\n \non\n \ntools\n \nneeded,\n \na\n \nsample\n \nstartup\n \nidea,\n \nand\n \nlive\n \ncode\n \nwalkthroughs. \n○\n \nInteractive\n \nelements\n \nincluded\n \nQ&A\n \nsessions.\n \nW orkshop (if any) \n●\n \nFigma\n \nDemonstration:\n \nLive\n \ndemonstration\n \nof\n \ncreating\n \nuser\n \ninterfaces\n \nusing\n \nFigma,\n \nshowcasing\n \nits\n \nfeatures\n \nfor\n \nUI\n \ndesign.\n \n●\n \nIonic\n \nFramework\n \nLive\n \nCode\n \nWalkthrough:\n \nPractical\n \ndemonstration\n \nof\n \nsetting\n \nup\n \nan\n \nIonic\n \nproject,\n \ncreating\n \nbasic\n \napp\n \nstructures,\n \nadding\n \ncomponents,\n \nand\n \ncoding\n \nessential\n \nfunctionalities.\n  Output expected/agreed with speciﬁc timeline: \n●\n \nIntroduction\n \nto\n \nUser\n \nInterface\n \n(UI)\n \n&\n \nUser\n \nExperience\n \n(UX)\n \nDesign\n \nsession,\n \nparticipants\n \nare\n \nexpected\n \nto:\n a. Understand the fundamental concepts and diﬀerences between User Interface \n(UI)\n \nand\n \nUser\n \nExperience\n \n(UX)\n \ndesign.\n b. Identify key principles of eﬀective interface and experience design. c. Gain initial exposure to UI design tools like Figma through demonstration. d. Appreciate the importance of UI/UX in the overall application development \nlifecycle.\n  \n●\n \nIntroduction\n \nto\n \nApplication\n \nDevelopment\n \nfor\n \nStartups\n \nsession,\n \nparticipants\n \nare\n \nexpected\n \nto:\n \n\n○\n \n \na. Demonstrate a clear understanding of the mobile app development process using \nthe\n \nIonic\n \nFramework,\n \nincluding\n \nproject\n \nsetup\n \nand\n \nworkﬂow.\n b. Identify and conﬁgure the necessary tools and development environment for \nbuilding\n \nand\n \ndeploying\n \ncross-platform\n \nmobile\n \napplications.\n c. Understand the process of creating an Ionic app, from adding functional pages to \nestablishing\n \na\n \nbackend\n \nserver\n \nconnection.\n  Challenges \n●\n \nTime\n \nConstraints:\n \nGiven\n \nthe\n \ncomprehensive\n \nnature\n \nof\n \nboth\n \nUI/UX\n \ndesign\n \nand\n \napplication\n \ndevelopment,\n \nthere\n \nwere\n \ninherent\n \ntime\n \nconstraints.\n \nThis\n \nlimited\n \nthe\n \ndepth\n \nto\n \nwhich\n \neach\n \nsub-topic\n \nand\n \ndemonstration\n \ncould\n \nbe\n \nexplored,\n \nparticularly\n \nfor\n \nextensive\n \nhands-on\n \nsegments\n \nor\n \nmore\n \ndetailed\n \nQ&A.\n  Recommendation \n●\n \nConsider\n \noﬀering\n \nseparate,\n \nmore\n \nin-depth\n \nworkshops\n \nfor\n \nFigma\n \nand\n \nIonic\n \nFramework\n \nto\n \nallow\n \nfor\n \nmore\n \nhands-on\n \npractice\n \nby\n \nparticipants.\n \n \n●\n \nExplore\n \nproviding\n \npre-session\n \nmaterials\n \non\n \nbasic\n \nconcepts\n \nto\n \nallow\n \nmore\n \ntime\n \nfor\n \npractical\n \ndemonstrations\n \nduring\n \nthe\n \nsession.\n    PHOTO DOCUM ENTATION: \nM orning Session (Introduction to User Interface (UI) & User Experience (UX) Design):                      Afternoon Session (Introduction to Application Development for Startups):              \n\n○\n \n \n               Prepared by:       Noted by:    JAYKEE M . ABA-A      ENGR. ELIZALDE S. RAM OS \nProject\n \nDevelopment\n \nOﬃcer\n \nI\n \n \n \n \n \nITO\n \nI\n \n-\n \nProvincial\n \nOﬃcer,\n \nSDS", "metadata": {"size": 9803903}}], "text_content": [{"filename": "INFO_SESSION__App_Development_for_Startupssigned_1.pdf", "content": "ACTIVITY DESIGN  \n Name of Activity : Online Info Session on Introduction to Application Development for    Startups Date   : June 2, 2025 Time   : 1:00PM - 5:00PM Venue   : Online via zoom Target Participants : 100 participants from LGUs, NGA, SUCs, and Residents in Surigao del Sur  Rationale/Background of the Activity  As mandated by Republic Act No. 10844, the Department of Information and Communications \nTechnology\n \n(DICT)\n \nis\n \ndirected\n \nto\n \nbe\n \nthe\n \nprimary\n \npolicy,\n \nplanning,\n \ncoordinating,\n \nimplementing,\n \nand\n \nadministrative\n \nentity\n \nof\n \nthe\n \nExecutive\n \nBranch\n \nof\n \nthe\n \ngovernment\n \nthat\n \nwill\n \nplan,\n \ndevelop,\n \nand\n \npromote\n \nthe\n \nnational\n \nICT\n \ndevelopment\n \nagenda.\n \nAmong\n \nothers,\n \nDICT\n \nis\n \nexpected\n \nto\n \nstrengthen\n \nits\n \neﬀorts\n \nin\n \nthe\n \nnation's\n \ncapacity\n \nbuilding\n \nthrough\n \nICT.\n  In line with this mandate, this training aims to equip aspiring developers, startups, and \nentrepreneurs\n \nwith\n \nessential\n \nknowledge\n \nand\n \nskills\n \nin\n \nmobile\n \napp\n \ndevelopment.\n \nBy\n \nintroducing\n \nparticipants\n \nto\n \nmodern\n \ntools\n \nlike\n \nthe\n \nIonic\n \nFramework\n \nand\n \nguiding\n \nthem\n \nthrough\n \nthe\n \napp\n \ndevelopment\n \nprocess—from\n \nsetup\n \nto\n \ndeployment—this\n \nsession\n \ncontributes\n \nto\n \nfostering\n \ninnovation,\n \ndigital\n \nentrepreneurship,\n \nand\n \nICT\n \nempowerment\n \nin\n \nthe\n \nPhilippine.\n  Objectives  At the end of this program, participants will be able to:  \n●\n \nUnderstand\n \nthe\n \nkey\n \nsteps\n \nin\n \ndeveloping\n \na\n \nmobile\n \napp\n \nusing\n \nthe\n \nIonic\n \nFramework.\n \n●\n \nIdentify\n \nthe\n \ntools\n \nand\n \nenvironment\n \nneeded\n \nto\n \ncreate\n \nand\n \ndeploy\n \na\n \ncross-platform\n \nmobile\n \napp.\n \n●\n \nObserve\n \nthe\n \nprocess\n \nof\n \ncreating\n \nan\n \nIonic\n \napp,\n \nadding\n \npages,\n \nand\n \nconnecting\n \nto\n \na\n \nbackend\n \nserver.\n \n●\n \nRecognize\n \nthe\n \nrequirements\n \nfor\n \ndeploying\n \nan\n \napp\n \nbackend\n \nonline\n \nand\n \npublishing\n \nan\n \nAndroid\n \napp\n \non\n \nthe\n \nGoogle\n \nPlay\n \nStore\n  Expected Output \nBy the end of this program, participants are expected to: \n●\n \nDemonstrate\n \na\n \nclear\n \nunderstanding\n \nof\n \nthe\n \nmobile\n \napp\n \ndevelopment\n \nprocess\n \nusing\n \nthe\n \nIonic\n \nFramework,\n \nincluding\n \nproject\n \nsetup\n \nand\n \nworkﬂow.\n \n●\n \nIdentify\n \nand\n \nconﬁgure\n \nthe\n \nnecessary\n \ntools\n \nand\n \ndevelopment\n \nenvironment\n \nfor\n \nbuilding\n \nand\n \ndeploying\n \ncross-platform\n \nmobile\n \napplications.\n \n \n●\n \nFollow\n \nand\n \nreplicate\n \nthe\n \ncreation\n \nof\n \nan\n \nIonic\n \napp\n,\n \nincluding\n \nadding\n \nfunctional\n \npages\n \nand\n \nestablishing\n \na\n \nconnection\n \nto\n \na\n \nbackend\n \nserver.\n \n  \n VERSION DATE  REVISED BY  CHANGES MADE \nv1.0 05/19/2025 Engr. Elizalde S. Ramos, Jr. Initial draft \n\n       Methodology  The program combines online sessions to deliver a strong learning experience. It integrates lectures, \ngroup\n \ndiscussions,\n \nand\n \nreal-world\n \ndemonstration\n \nto\n \nensure\n \ncomprehensive\n \nunderstanding\n \nand\n \npractical\n \napplication.\n  Duration  \nMode of Delivery No. of sessions  No. of hours / session (day) Total no. of Hours \nOnline 1 4 4 \n Pre-requisites  \n●\n \nMust\n \nhave\n \na\n \npersonal\n \ncomputer\n \nwith\n \nreliable\n \ninternet\n \nconnection.\n \n \n●\n \nWilling\n \nto\n \nﬁnish\n \nthe\n \nwhole\n \nduration\n \nof\n \nthe\n \nsession.\n \n \n●\n \nAims\n \nto\n \nenhance\n \ntheir\n \nskills\n \nin\n \nthe\n \nﬁeld\n \nof\n \nICT.\n \n  Targer Participants   100 participants from LGUs, NGA, SUCs, and Residents in Surigao del Sur   Indicative Program Flow  \nDetailed Course Outline \nModule Number Topic/s or Module/s: Instructional Material/s Exercises/ Assessments \nModule 1  (Time : 10–15 mins)   Introduction to App Development for Startup\n PowerPoint (overview of app development process, tools used) Short Q&A:  Why is app development important for startups? \nModule 2  (Time :35–40 mins )   Overview of Ionic Framework & Environment Setup Learning Module on Mobile Applications; PowerPoint; Live Code Walkthrough; VS Code, JDK, Android Studio Interactive poll:  Which platforms do you plan to deploy on?\n \nModule 3 (Time :20–25 mins ) Creating a New Ionic App (basic structure) Learning Module on Mobile Applications; Terminal/CLI Demo; Sample Code Snippets Mini activity: Guide them to list app components/features they plan to build\n \n \n\n      \nModule 4  (Time : 25–30 mins)  Adding App Pages & Navigation Learning Module on Mobile Applications; Code Examples (HTML, Ionic Components); VS Code Screenshots Reﬂective question: What pages will your startup app need? \nModule 5  (Time : 35–40 mins)  Connecting to Backend API (PHP + MySQL) Learning Module on Mobile Applications; XAMPP (Localhost Server); API Code & Database Group sharing:  What data will your app need from a backend? \nModule 6 (Time : 35–40 mins)  Deploying App Backend to DigitalOcean & Domain Linking Learning Module on Mobile Applications; FTP (FileZilla); Domain Name (GoDaddy); Live Server (DigitalOcean) Short quiz:  List 2 requirements for deploying an app backend online \nModule 7  (Time : 20–25 min)  Building Android App & Testing with Android Studio Learning Module on Mobile Applications; Android Studio for APK; Mobile Phone Open discussion: What is required to publish an app on Google Play Store? \nModule 8  (Time : 10–15 mins)  Overview of Google Play Store Publishing Process Learning Module on Mobile Applications; PowerPoint of Play Console Steps & Requirements Checklist Q&A:  What app details are required in a store listing? \nModule 9  (Time : 20–25 mins)  Wrap-Up, Open Forum, and Certiﬁcate Distribution Feedback form link; summary slide Completion acknowledgment; submission of feedback form \n Resource Requirements  A. Manpower  ● Course/Seminar Oﬃcer ● Resource Person. ● Facilities Assistant / Technical Support ● Administrative Assistant  B. Facilities/Equipment Training Room / Computer laboratory equipped with the following:  ● Laptops / desktops ● Adequate PA system ● Stable internet connection ● Large screen or LCD projector ● Whiteboard ● CCTV Camera \n \n\n       C. Training Materials  ● Training Schedule via Zoom  ● Presentation Materials ● Training Kit    Budget Estimate:   \nExpense Item  Particulars  Amount \nRPs Honoraria 4 hours virtual training * 1,400 per hour = 5,600.00 5,600.00 \nTraining Supplies  A4 Bond Paper *  5 reams * 280 = 1,400.00 Expanded Envelope * 20pcs * 30 =  600.00 Ballpoint pen * 2 box * 200  = 400.00 Non-permanent marker * 2pcs * 150 = 300.00  2,700.00 \nTOTAL  8,300.00 \n \nApproved by:          MARIO P. CUÑADO           Regional Director  \n Prepared by:    ENGR. ELIZALDE S. RAMOS, JR. Provincial Oﬃcer, SDS Concurred by:    ENGR. ALBERT C. GABRIEL  ITO II -  ILCDB Project Focal  \n  \nRecommending Approval:    ENGR. LAWRENCE P. SALANG Chief, Technical Operations Division   Certiﬁed Funds Available:    MARK CHRISTIAN L. ARIZALA Budget Oﬃcer II"}, {"filename": "Post_Activity_Report-Infosession-June_2_2025.pdf", "content": "○\n \n \nPOST-ACTIVITY REPORT   Activity Name:  Information Sessions on the following topics: \n●\n \nIntroduction\n \nto\n \nUser\n \nInterface\n \n(UI)\n \n&\n \nUser\n \nExperience\n \n(UX)\n \nDesign\n \n●\n \nIntroduction\n \nto\n \nApplication\n \nDevelopment\n \nfor\n \nStartups\n Venue: Zoom Date: June 02, 2025 Time: 8:00 AM - 5:00 PM  Activity Highlights \n●\n \nM orning\n \nSession\n \n(Introduction\n \nto\n \nUser\n \nInterface\n \n(UI)\n \n&\n \nUser\n \nExperience\n \n(UX)\n \nDesign): \n○\n \nParticipants\n \nwere\n \nintroduced\n \nto\n \nthe\n \nfundamental\n \nconcepts\n \nof\n \nUser\n \nInterface\n \n(UI)\n \nand\n \nUser\n \nExperience\n \n(UX)\n \ndesign. \n○\n \nKey\n \nprinciples,\n \nhistorical\n \ncontext,\n \nand\n \nthe\n \nimportance\n \nof\n \nUI/UX\n \nin\n \nmodern\n \napplications\n \nwere\n \ndiscussed. \n○\n \nA\n \npractical\n \ndemonstration\n \nwas\n \nconducted\n \non\n \ncreating\n \napplication\n \ninterfaces\n \nusing\n \nFigma,\n \ncovering\n \nbasic\n \ntools\n \nand\n \nworkﬂows. \n○\n \nTopics\n \nincluded\n \nthe\n \ndiﬀerences\n \nbetween\n \nUI\n \nand\n \nUX,\n \nwhy\n \nUI/UX\n \nis\n \nimportant,\n \ngood\n \nvs.\n \nbad\n \ndesign\n \nexamples,\n \nand\n \nkey\n \nprinciples\n \nof\n \nUX\n \ndesign.\n  \n●\n \nAfternoon\n \nSession\n \n(Introduction\n \nto\n \nApplication\n \nDevelopment\n \nfor\n \nStartups): \n○\n \nFocused\n \non\n \nequipping\n \naspiring\n \ndevelopers,\n \nstartups,\n \nand\n \nentrepreneurs\n \nwith\n \nessential\n \nknowledge\n \nand\n \nskills\n \nin\n \nmobile\n \napp\n \ndevelopment. \n○\n \nIntroduction\n \nto\n \nthe\n \nIonic\n \nFramework\n \nfor\n \ncross-platform\n \nmobile\n \napp\n \ndevelopment. \n○\n \nCovered\n \nthe\n \napp\n \ndevelopment\n \nlifecycle\n \nfrom\n \nsetup\n \nto\n \ndeployment,\n \nincluding: \n■\n \nOverview\n \nof\n \nIonic\n \nFramework\n \nand\n \nenvironment\n \nsetup\n \n(VS\n \nCode,\n \nJDK,\n \nAndroid\n \nStudio). \n■\n \nCreating\n \na\n \nnew\n \nIonic\n \napp\n \n(basic\n \nstructure,\n \nTerminal/CLI\n \ndemo). \n■\n \nAdding\n \napp\n \npages,\n \nnavigation,\n \nand\n \nconnecting\n \nto\n \na\n \nbackend\n \nAPI\n \n(PHP\n \n+\n \nMySQL,\n \nXAMPP). \n■\n \nDeploying\n \nan\n \napp\n \nbackend\n \n(FTP/FileZilla,\n \ndomain\n \nname,\n \nDigitalOcean). \n■\n \nBuilding\n \nand\n \ntesting\n \nan\n \nAndroid\n \nApp\n \n(Android\n \nStudio\n \nfor\n \nAPK). \n○\n \nIncluded\n \ndiscussions\n \non\n \ntools\n \nneeded,\n \na\n \nsample\n \nstartup\n \nidea,\n \nand\n \nlive\n \ncode\n \nwalkthroughs. \n○\n \nInteractive\n \nelements\n \nincluded\n \nQ&A\n \nsessions.\n \nW orkshop (if any) \n●\n \nFigma\n \nDemonstration:\n \nLive\n \ndemonstration\n \nof\n \ncreating\n \nuser\n \ninterfaces\n \nusing\n \nFigma,\n \nshowcasing\n \nits\n \nfeatures\n \nfor\n \nUI\n \ndesign.\n \n●\n \nIonic\n \nFramework\n \nLive\n \nCode\n \nWalkthrough:\n \nPractical\n \ndemonstration\n \nof\n \nsetting\n \nup\n \nan\n \nIonic\n \nproject,\n \ncreating\n \nbasic\n \napp\n \nstructures,\n \nadding\n \ncomponents,\n \nand\n \ncoding\n \nessential\n \nfunctionalities.\n  Output expected/agreed with speciﬁc timeline: \n●\n \nIntroduction\n \nto\n \nUser\n \nInterface\n \n(UI)\n \n&\n \nUser\n \nExperience\n \n(UX)\n \nDesign\n \nsession,\n \nparticipants\n \nare\n \nexpected\n \nto:\n a. Understand the fundamental concepts and diﬀerences between User Interface \n(UI)\n \nand\n \nUser\n \nExperience\n \n(UX)\n \ndesign.\n b. Identify key principles of eﬀective interface and experience design. c. Gain initial exposure to UI design tools like Figma through demonstration. d. Appreciate the importance of UI/UX in the overall application development \nlifecycle.\n  \n●\n \nIntroduction\n \nto\n \nApplication\n \nDevelopment\n \nfor\n \nStartups\n \nsession,\n \nparticipants\n \nare\n \nexpected\n \nto:\n \n\n○\n \n \na. Demonstrate a clear understanding of the mobile app development process using \nthe\n \nIonic\n \nFramework,\n \nincluding\n \nproject\n \nsetup\n \nand\n \nworkﬂow.\n b. Identify and conﬁgure the necessary tools and development environment for \nbuilding\n \nand\n \ndeploying\n \ncross-platform\n \nmobile\n \napplications.\n c. Understand the process of creating an Ionic app, from adding functional pages to \nestablishing\n \na\n \nbackend\n \nserver\n \nconnection.\n  Challenges \n●\n \nTime\n \nConstraints:\n \nGiven\n \nthe\n \ncomprehensive\n \nnature\n \nof\n \nboth\n \nUI/UX\n \ndesign\n \nand\n \napplication\n \ndevelopment,\n \nthere\n \nwere\n \ninherent\n \ntime\n \nconstraints.\n \nThis\n \nlimited\n \nthe\n \ndepth\n \nto\n \nwhich\n \neach\n \nsub-topic\n \nand\n \ndemonstration\n \ncould\n \nbe\n \nexplored,\n \nparticularly\n \nfor\n \nextensive\n \nhands-on\n \nsegments\n \nor\n \nmore\n \ndetailed\n \nQ&A.\n  Recommendation \n●\n \nConsider\n \noﬀering\n \nseparate,\n \nmore\n \nin-depth\n \nworkshops\n \nfor\n \nFigma\n \nand\n \nIonic\n \nFramework\n \nto\n \nallow\n \nfor\n \nmore\n \nhands-on\n \npractice\n \nby\n \nparticipants.\n \n \n●\n \nExplore\n \nproviding\n \npre-session\n \nmaterials\n \non\n \nbasic\n \nconcepts\n \nto\n \nallow\n \nmore\n \ntime\n \nfor\n \npractical\n \ndemonstrations\n \nduring\n \nthe\n \nsession.\n    PHOTO DOCUM ENTATION: \nM orning Session (Introduction to User Interface (UI) & User Experience (UX) Design):                      Afternoon Session (Introduction to Application Development for Startups):              \n\n○\n \n \n               Prepared by:       Noted by:    JAYKEE M . ABA-A      ENGR. ELIZALDE S. RAM OS \nProject\n \nDevelopment\n \nOﬃcer\n \nI\n \n \n \n \n \nITO\n \nI\n \n-\n \nProvincial\n \nOﬃcer,\n \nSDS"}], "image_files": [], "total_files": 2, "text_files_count": 2, "image_files_count": 0}, "structured_data": {"training_details": {"course_title": "fdre", "course_code": "rer", "date": "2025-06-28", "time": "8:00", "duration": "8", "venue": "", "resource_person": "", "platform_used": "", "mode": "Online", "target_participants": "DICT Personnel", "total_attendees": "", "attendees_male": "", "attendees_female": "", "sector_categories": {"nga": {"male": "", "female": ""}, "lgu": {"male": "", "female": ""}, "suc": {"male": "", "female": ""}, "others": {"male": "", "female": ""}}, "total_certificates": {"male": "", "female": ""}}, "rationale": "", "objectives": "", "topics_covered": "", "issues_and_concerns": "", "recommendations": "", "plans_and_action_items": "", "photo_documentation": [], "prepared_by": {"name": "JAYKEE M.ABA-A", "position": "Project Development Officer I"}, "noted_by": {"name": "ENGR. ELIZALDE S. RAMOS", "position": "ITO I - Provincial Officer, SDS"}}, "created_at": "2025-06-28T23:10:32.444556", "modified_at": "2025-06-28T23:11:11.723331"}
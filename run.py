#!/usr/bin/env python3
"""
Run script for the After Training Report Generator
"""

import os
import sys
from app import app
from config import Config

def main():
    """Main entry point for the application"""
    
    # Check for required environment variables
    if not os.environ.get('GEMINI_API_KEY'):
        print("ERROR: GEMINI_API_KEY environment variable is required")
        print("Please set your Google Gemini API key:")
        print("export GEMINI_API_KEY='your-api-key-here'")
        sys.exit(1)
    
    # Initialize configuration
    Config.init_app(app)
    
    # Print startup information
    print("=" * 60)
    print("After Training Report Generator")
    print("=" * 60)
    print(f"Upload folder: {app.config['UPLOAD_FOLDER']}")
    print(f"Reports folder: {app.config['REPORTS_FOLDER']}")
    print(f"Max file size: {app.config['MAX_CONTENT_LENGTH'] / (1024*1024):.0f}MB")
    print("=" * 60)
    print("Starting server...")
    print("Access the application at: http://localhost:5000")
    print("=" * 60)
    
    # Run the application
    try:
        app.run(
            debug=True,
            host='0.0.0.0',
            port=5000,
            threaded=True
        )
    except KeyboardInterrupt:
        print("\nShutting down server...")
    except Exception as e:
        print(f"Error starting server: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()

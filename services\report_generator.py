import os
import json
from datetime import datetime
from typing import Dict, Any, List
import logging
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image as RLImage
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
from jinja2 import Template
import weasyprint

logger = logging.getLogger(__name__)

class ReportGenerator:
    """Service for generating formatted reports from structured data"""
    
    def __init__(self):
        self.styles = getSampleStyleSheet()
        self._setup_custom_styles()
    
    def _setup_custom_styles(self):
        """Setup custom styles for the report"""
        # Header style
        self.styles.add(ParagraphStyle(
            name='ReportHeader',
            parent=self.styles['Heading1'],
            fontSize=14,
            spaceAfter=20,
            alignment=TA_CENTER,
            fontName='Helvetica-Bold'
        ))
        
        # Section header style
        self.styles.add(ParagraphStyle(
            name='SectionHeader',
            parent=self.styles['Heading2'],
            fontSize=12,
            spaceAfter=10,
            spaceBefore=15,
            fontName='Helvetica-Bold',
            backColor=colors.lightgrey,
            borderWidth=1,
            borderColor=colors.black,
            leftIndent=5,
            rightIndent=5,
            topPadding=5,
            bottomPadding=5
        ))
        
        # Table cell style
        self.styles.add(ParagraphStyle(
            name='TableCell',
            parent=self.styles['Normal'],
            fontSize=10,
            fontName='Helvetica'
        ))
        
        # Signature style
        self.styles.add(ParagraphStyle(
            name='Signature',
            parent=self.styles['Normal'],
            fontSize=10,
            fontName='Helvetica-Bold',
            alignment=TA_CENTER
        ))
    
    def generate_report(self, structured_data: Dict[str, Any], session_id: str, output_folder: str) -> str:
        """Generate the complete After Training Report"""
        try:
            # Create output filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_filename = f"after_training_report_{session_id}_{timestamp}.pdf"
            output_path = os.path.join(output_folder, output_filename)
            
            # Generate using HTML/CSS approach for better formatting
            html_content = self._generate_html_report(structured_data)
            
            # Convert HTML to PDF using WeasyPrint
            weasyprint.HTML(string=html_content).write_pdf(output_path)
            
            logger.info(f"Report generated successfully: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"Error generating report: {str(e)}")
            # Fallback to ReportLab if WeasyPrint fails
            return self._generate_reportlab_pdf(structured_data, session_id, output_folder)
    
    def _generate_html_report(self, data: Dict[str, Any]) -> str:
        """Generate HTML report that matches the template exactly"""
        html_template = """
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>After Training Report</title>
    <style>
        @page {
            size: A4;
            margin: 0.5in;
        }
        
        body {
            font-family: Arial, sans-serif;
            font-size: 11px;
            line-height: 1.2;
            margin: 0;
            padding: 0;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 10px;
        }
        
        .dept-name {
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .report-title {
            font-size: 16px;
            font-weight: bold;
            margin: 20px 0;
        }
        
        .section {
            margin-bottom: 15px;
        }
        
        .section-header {
            background-color: #f0f0f0;
            border: 1px solid #000;
            padding: 5px;
            font-weight: bold;
            font-size: 12px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 10px;
        }
        
        td, th {
            border: 1px solid #000;
            padding: 5px;
            vertical-align: top;
            font-size: 10px;
        }
        
        .field-label {
            font-weight: bold;
            background-color: #f8f8f8;
            width: 150px;
        }
        
        .field-value {
            min-height: 20px;
        }
        
        .two-column {
            display: flex;
        }
        
        .column {
            flex: 1;
            margin-right: 10px;
        }
        
        .column:last-child {
            margin-right: 0;
        }
        
        .signature-section {
            margin-top: 40px;
            display: flex;
            justify-content: space-between;
        }
        
        .signature-block {
            text-align: center;
            width: 45%;
        }
        
        .signature-line {
            border-bottom: 1px solid #000;
            margin-bottom: 5px;
            height: 40px;
        }
        
        .attendees-table td {
            text-align: center;
        }
        
        .photo-section {
            min-height: 100px;
            border: 1px solid #000;
            padding: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="dept-name">REPUBLIC OF THE PHILIPPINES</div>
        <div class="dept-name">DEPARTMENT OF INFORMATION AND</div>
        <div class="dept-name">COMMUNICATIONS TECHNOLOGY</div>
        <div class="report-title">AFTER TRAINING REPORT</div>
    </div>
    
    <div class="section">
        <div class="section-header">I. TRAINING DETAILS</div>
        <table>
            <tr>
                <td class="field-label">Course Title:</td>
                <td class="field-value">{{ data.training_details.course_title }}</td>
            </tr>
            <tr>
                <td class="field-label">Course Code:</td>
                <td class="field-value">{{ data.training_details.course_code }}</td>
            </tr>
            <tr>
                <td class="field-label">Date:</td>
                <td class="field-value">{{ data.training_details.date }}</td>
                <td class="field-label" style="width: 80px;">Time:</td>
                <td class="field-value">{{ data.training_details.time }}</td>
            </tr>
            <tr>
                <td class="field-label">Duration:</td>
                <td class="field-value" colspan="3">{{ data.training_details.duration }}</td>
            </tr>
            <tr>
                <td class="field-label">Venue:</td>
                <td class="field-value" colspan="3">{{ data.training_details.venue }}</td>
            </tr>
            <tr>
                <td class="field-label">Resource Person:</td>
                <td class="field-value" colspan="3">{{ data.training_details.resource_person }}</td>
            </tr>
            <tr>
                <td class="field-label">Platform Used:</td>
                <td class="field-value">{{ data.training_details.platform_used }}</td>
                <td class="field-label">Mode:</td>
                <td class="field-value">{{ data.training_details.mode }}</td>
            </tr>
            <tr>
                <td class="field-label">Target Participants:</td>
                <td class="field-value" colspan="3">{{ data.training_details.target_participants }}</td>
            </tr>
        </table>
        
        <table class="attendees-table">
            <tr>
                <td class="field-label">Total # of Attendees:</td>
                <td class="field-value">{{ data.training_details.total_attendees }}</td>
                <td class="field-label">Male</td>
                <td class="field-value">{{ data.training_details.attendees_male }}</td>
                <td class="field-label">Female</td>
                <td class="field-value">{{ data.training_details.attendees_female }}</td>
            </tr>
            <tr>
                <td colspan="6" style="text-align: center; font-weight: bold;">Number of Beneficiary/ies with Sex Disaggregation</td>
            </tr>
            <tr>
                <td class="field-label">Sector Category:</td>
                <td></td>
                <td class="field-label">Male</td>
                <td></td>
                <td class="field-label">Female</td>
                <td></td>
            </tr>
            <tr>
                <td></td>
                <td class="field-label">NGA:</td>
                <td class="field-value">{{ data.training_details.sector_categories.nga.male }}</td>
                <td class="field-label">Male</td>
                <td class="field-value">{{ data.training_details.sector_categories.nga.female }}</td>
                <td class="field-label">Female</td>
            </tr>
            <tr>
                <td></td>
                <td class="field-label">LGU:</td>
                <td class="field-value">{{ data.training_details.sector_categories.lgu.male }}</td>
                <td class="field-label">Male</td>
                <td class="field-value">{{ data.training_details.sector_categories.lgu.female }}</td>
                <td class="field-label">Female</td>
            </tr>
            <tr>
                <td></td>
                <td class="field-label">SUC:</td>
                <td class="field-value">{{ data.training_details.sector_categories.suc.male }}</td>
                <td class="field-label">Male</td>
                <td class="field-value">{{ data.training_details.sector_categories.suc.female }}</td>
                <td class="field-label">Female</td>
            </tr>
            <tr>
                <td></td>
                <td class="field-label">Others:</td>
                <td class="field-value">{{ data.training_details.sector_categories.others.male }}</td>
                <td class="field-label">Male</td>
                <td class="field-value">{{ data.training_details.sector_categories.others.female }}</td>
                <td class="field-label">Female</td>
            </tr>
            <tr>
                <td class="field-label">Total # of Issued Certificates:</td>
                <td></td>
                <td class="field-label">Male</td>
                <td class="field-value">{{ data.training_details.total_certificates.male }}</td>
                <td class="field-label">Female</td>
                <td class="field-value">{{ data.training_details.total_certificates.female }}</td>
            </tr>
        </table>
    </div>
    
    <div class="section">
        <div class="section-header">II. RATIONALE</div>
        <div style="border: 1px solid #000; padding: 10px; min-height: 60px;">
            {{ data.rationale }}
        </div>
    </div>
    
    <div class="section">
        <div class="section-header">III. OBJECTIVES</div>
        <div style="border: 1px solid #000; padding: 10px; min-height: 60px;">
            {{ data.objectives }}
        </div>
    </div>
    
    <div class="section">
        <div class="section-header">IV. Topics Covered:</div>
        <div style="border: 1px solid #000; padding: 10px; min-height: 80px;">
            {{ data.topics_covered }}
        </div>
    </div>
    
    <div class="two-column">
        <div class="column">
            <div class="section-header">V. ISSUES AND CONCERNS</div>
            <div style="border: 1px solid #000; padding: 10px; min-height: 100px;">
                {{ data.issues_and_concerns }}
            </div>
        </div>
        <div class="column">
            <div class="section-header">VI. RECOMMENDATION</div>
            <div style="border: 1px solid #000; padding: 10px; min-height: 100px;">
                {{ data.recommendations }}
            </div>
        </div>
    </div>
    
    <div class="section">
        <div class="section-header">VII. PLANS AND ACTION ITEMS (NEXT STEPS)</div>
        <div style="border: 1px solid #000; padding: 10px; min-height: 80px;">
            {{ data.plans_and_action_items }}
        </div>
    </div>
    
    <div class="section">
        <div class="section-header">VIII. PHOTO DOCUMENTATION</div>
        <div class="photo-section">
            <!-- Photo documentation area -->
        </div>
    </div>
    
    <div class="signature-section">
        <div class="signature-block">
            <div>Prepared by:</div>
            <div class="signature-line"></div>
            <div style="font-weight: bold;">{{ data.prepared_by.name }}</div>
            <div>{{ data.prepared_by.position }}</div>
        </div>
        <div class="signature-block">
            <div>Noted by:</div>
            <div class="signature-line"></div>
            <div style="font-weight: bold;">{{ data.noted_by.name }}</div>
            <div>{{ data.noted_by.position }}</div>
        </div>
    </div>
</body>
</html>
        """
        
        template = Template(html_template)
        return template.render(data=data)
    
    def _generate_reportlab_pdf(self, data: Dict[str, Any], session_id: str, output_folder: str) -> str:
        """Fallback PDF generation using ReportLab"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_filename = f"after_training_report_{session_id}_{timestamp}_fallback.pdf"
            output_path = os.path.join(output_folder, output_filename)
            
            doc = SimpleDocTemplate(output_path, pagesize=A4)
            story = []
            
            # Header
            story.append(Paragraph("REPUBLIC OF THE PHILIPPINES", self.styles['ReportHeader']))
            story.append(Paragraph("DEPARTMENT OF INFORMATION AND", self.styles['ReportHeader']))
            story.append(Paragraph("COMMUNICATIONS TECHNOLOGY", self.styles['ReportHeader']))
            story.append(Spacer(1, 20))
            story.append(Paragraph("AFTER TRAINING REPORT", self.styles['ReportHeader']))
            story.append(Spacer(1, 30))
            
            # Training Details Section
            story.append(Paragraph("I. TRAINING DETAILS", self.styles['SectionHeader']))
            
            training_data = [
                ['Course Title:', data['training_details']['course_title']],
                ['Course Code:', data['training_details']['course_code']],
                ['Date:', data['training_details']['date'], 'Time:', data['training_details']['time']],
                ['Duration:', data['training_details']['duration']],
                ['Venue:', data['training_details']['venue']],
                ['Resource Person:', data['training_details']['resource_person']],
                ['Platform Used:', data['training_details']['platform_used'], 'Mode:', data['training_details']['mode']],
                ['Target Participants:', data['training_details']['target_participants']]
            ]
            
            training_table = Table(training_data, colWidths=[1.5*inch, 2*inch, 1*inch, 1.5*inch])
            training_table.setStyle(TableStyle([
                ('BORDER', (0, 0), (-1, -1), 1, colors.black),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
                ('BACKGROUND', (2, 2), (2, 2), colors.lightgrey),
            ]))
            
            story.append(training_table)
            story.append(Spacer(1, 20))
            
            # Add other sections
            sections = [
                ('II. RATIONALE', data['rationale']),
                ('III. OBJECTIVES', data['objectives']),
                ('IV. Topics Covered:', data['topics_covered']),
                ('V. ISSUES AND CONCERNS', data['issues_and_concerns']),
                ('VI. RECOMMENDATION', data['recommendations']),
                ('VII. PLANS AND ACTION ITEMS (NEXT STEPS)', data['plans_and_action_items'])
            ]
            
            for section_title, content in sections:
                story.append(Paragraph(section_title, self.styles['SectionHeader']))
                story.append(Paragraph(content or '', self.styles['Normal']))
                story.append(Spacer(1, 15))
            
            # Photo Documentation
            story.append(Paragraph("VIII. PHOTO DOCUMENTATION", self.styles['SectionHeader']))
            story.append(Spacer(1, 50))
            
            # Signatures
            story.append(Spacer(1, 30))
            sig_data = [
                ['Prepared by:', '', 'Noted by:'],
                ['', '', ''],
                [data['prepared_by']['name'], '', data['noted_by']['name']],
                [data['prepared_by']['position'], '', data['noted_by']['position']]
            ]
            
            sig_table = Table(sig_data, colWidths=[2.5*inch, 1*inch, 2.5*inch])
            sig_table.setStyle(TableStyle([
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 2), (-1, -1), 'Helvetica-Bold'),
                ('LINEBELOW', (0, 1), (0, 1), 1, colors.black),
                ('LINEBELOW', (2, 1), (2, 1), 1, colors.black),
            ]))
            
            story.append(sig_table)
            
            doc.build(story)
            logger.info(f"Fallback report generated: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"Error generating fallback PDF: {str(e)}")
            raise

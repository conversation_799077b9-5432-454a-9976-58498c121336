# Deployment Guide - After Training Report Generator

## Prerequisites

### 1. Python Installation
- **Windows**: Download from [python.org](https://www.python.org/downloads/) (Python 3.8+)
- **macOS**: Use Homebrew: `brew install python3`
- **Linux**: Use package manager: `sudo apt install python3 python3-pip`

### 2. Google Gemini API Key
1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Save the key securely (you'll need it for setup)

## Quick Setup

### Option 1: Automated Setup
```bash
# 1. Navigate to the project directory
cd report_generator

# 2. Run the setup script
python setup.py

# 3. Set your API key (replace with your actual key)
export GEMINI_API_KEY="your-api-key-here"

# 4. Start the application
python run.py
```

### Option 2: Manual Setup
```bash
# 1. Install dependencies
pip install -r requirements.txt

# 2. Create directories
mkdir uploads reports

# 3. Set environment variable
export GEMINI_API_KEY="your-api-key-here"

# 4. Run the application
python run.py
```

## Environment Variables

Create a `.env` file in the project root:

```bash
# Copy the example file
cp .env.example .env

# Edit the .env file with your settings
GEMINI_API_KEY=your-actual-api-key-here
SECRET_KEY=your-secret-key-for-production
FLASK_ENV=development
```

## Windows-Specific Setup

### Using Command Prompt:
```cmd
# Set environment variable
set GEMINI_API_KEY=your-api-key-here

# Run the application
python run.py
```

### Using PowerShell:
```powershell
# Set environment variable
$env:GEMINI_API_KEY="your-api-key-here"

# Run the application
python run.py
```

### Permanent Environment Variable (Windows):
1. Open System Properties → Advanced → Environment Variables
2. Add new User Variable:
   - Name: `GEMINI_API_KEY`
   - Value: `your-api-key-here`
3. Restart your command prompt/PowerShell

## Production Deployment

### Using Gunicorn (Linux/macOS)
```bash
# Install gunicorn
pip install gunicorn

# Run with gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

### Using Docker
```dockerfile
# Create Dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 5000

CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:5000", "app:app"]
```

```bash
# Build and run
docker build -t report-generator .
docker run -p 5000:5000 -e GEMINI_API_KEY=your-key report-generator
```

### Nginx Configuration (Production)
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    client_max_body_size 50M;
}
```

## Testing the Installation

### 1. Run Tests
```bash
python test_app.py
```

### 2. Health Check
```bash
# Start the application, then test
curl http://localhost:5000/health
```

### 3. Manual Test
1. Open browser to `http://localhost:5000`
2. Upload a sample text file with training information
3. Verify the preview page loads
4. Generate a test report

## Troubleshooting

### Common Issues

#### 1. "GEMINI_API_KEY not found"
**Solution**: Ensure the environment variable is set correctly
```bash
# Check if set
echo $GEMINI_API_KEY

# Set if missing
export GEMINI_API_KEY="your-key-here"
```

#### 2. "Module not found" errors
**Solution**: Install missing dependencies
```bash
pip install -r requirements.txt
```

#### 3. Permission errors on uploads/reports directories
**Solution**: Check directory permissions
```bash
chmod 755 uploads reports
```

#### 4. WeasyPrint installation issues
**Solution**: Install system dependencies

**Ubuntu/Debian**:
```bash
sudo apt-get install python3-dev python3-pip python3-cffi python3-brotli libpango-1.0-0 libharfbuzz0b libpangoft2-1.0-0
```

**macOS**:
```bash
brew install pango
```

**Windows**: WeasyPrint should work with the pip installation

#### 5. Large file upload failures
**Solution**: Increase server limits
- Check `MAX_CONTENT_LENGTH` in config.py
- For production, configure web server limits

### Performance Optimization

#### 1. File Cleanup
Add a cleanup script for old files:
```python
# cleanup.py
import os
import time
from datetime import datetime, timedelta

def cleanup_old_files(directory, days=7):
    cutoff = time.time() - (days * 24 * 60 * 60)
    for filename in os.listdir(directory):
        filepath = os.path.join(directory, filename)
        if os.path.getctime(filepath) < cutoff:
            os.remove(filepath)

# Run cleanup
cleanup_old_files('uploads')
cleanup_old_files('reports')
```

#### 2. Caching
Consider implementing Redis for session caching in production.

#### 3. Load Balancing
For high traffic, use multiple Gunicorn workers and a load balancer.

## Security Considerations

### 1. API Key Security
- Never commit API keys to version control
- Use environment variables or secure key management
- Rotate keys regularly

### 2. File Upload Security
- Validate file types and sizes
- Scan uploaded files for malware
- Implement rate limiting

### 3. Production Security
- Use HTTPS in production
- Implement user authentication
- Set up proper logging and monitoring
- Regular security updates

## Monitoring and Logging

### 1. Application Logs
```python
# Add to app.py for production logging
import logging
from logging.handlers import RotatingFileHandler

if not app.debug:
    file_handler = RotatingFileHandler('logs/app.log', maxBytes=10240, backupCount=10)
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
    ))
    file_handler.setLevel(logging.INFO)
    app.logger.addHandler(file_handler)
```

### 2. Health Monitoring
Set up monitoring for:
- Application uptime
- Response times
- Error rates
- Disk space (uploads/reports directories)
- API quota usage (Gemini API)

## Backup and Recovery

### 1. Data Backup
- Regular backups of uploads and reports directories
- Database backups if using persistent storage
- Configuration backups

### 2. Disaster Recovery
- Document recovery procedures
- Test backup restoration
- Have rollback plans for updates

## Support and Maintenance

### 1. Regular Updates
- Keep Python and dependencies updated
- Monitor security advisories
- Test updates in staging environment

### 2. Monitoring
- Set up alerts for errors
- Monitor API usage and costs
- Track application performance

### 3. User Support
- Provide user documentation
- Set up support channels
- Collect user feedback for improvements

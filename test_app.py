import unittest
import os
import json
import tempfile
import shutil
from unittest.mock import patch, MagicMock
from app import app
from services.document_parser import DocumentParser
from services.gemini_service import GeminiService
from services.report_generator import ReportGenerator

class TestReportGenerator(unittest.TestCase):
    """Test suite for the report generation system"""
    
    def setUp(self):
        """Set up test environment"""
        self.app = app
        self.app.config['TESTING'] = True
        self.app.config['UPLOAD_FOLDER'] = tempfile.mkdtemp()
        self.app.config['REPORTS_FOLDER'] = tempfile.mkdtemp()
        self.client = self.app.test_client()
        
        # Create test directories
        os.makedirs(self.app.config['UPLOAD_FOLDER'], exist_ok=True)
        os.makedirs(self.app.config['REPORTS_FOLDER'], exist_ok=True)
        
    def tearDown(self):
        """Clean up test environment"""
        shutil.rmtree(self.app.config['UPLOAD_FOLDER'], ignore_errors=True)
        shutil.rmtree(self.app.config['REPORTS_FOLDER'], ignore_errors=True)
    
    def test_index_page(self):
        """Test the main upload page loads correctly"""
        response = self.client.get('/')
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'Upload Training Documents', response.data)
    
    def test_health_check(self):
        """Test health check endpoint"""
        response = self.client.get('/health')
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertEqual(data['status'], 'healthy')
    
    def test_upload_no_files(self):
        """Test upload endpoint with no files"""
        response = self.client.post('/upload')
        self.assertEqual(response.status_code, 400)
        data = json.loads(response.data)
        self.assertIn('error', data)
    
    def test_upload_empty_files(self):
        """Test upload endpoint with empty files"""
        response = self.client.post('/upload', data={'files': []})
        self.assertEqual(response.status_code, 400)
        data = json.loads(response.data)
        self.assertIn('error', data)
    
    def create_test_file(self, filename, content):
        """Helper method to create test files"""
        filepath = os.path.join(self.app.config['UPLOAD_FOLDER'], filename)
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
        return filepath
    
    @patch('services.gemini_service.GeminiService.structure_training_data')
    def test_upload_valid_files(self, mock_gemini):
        """Test upload endpoint with valid files"""
        # Mock Gemini response
        mock_gemini.return_value = {
            'training_details': {
                'course_title': 'Test Training',
                'course_code': 'TEST001'
            },
            'rationale': 'Test rationale'
        }
        
        # Create test file
        test_content = "Training Title: Python Programming\nDate: 2024-01-15\nObjectives: Learn Python basics"
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write(test_content)
            f.flush()
            
            with open(f.name, 'rb') as test_file:
                response = self.client.post('/upload', 
                    data={'files': (test_file, 'test_training.txt')},
                    content_type='multipart/form-data'
                )
        
        os.unlink(f.name)
        
        # Note: This test may fail without proper Gemini API key
        # In a real environment, you'd mock the entire service
        if response.status_code == 500:
            # Expected if Gemini API key is not configured
            self.assertIn(b'error', response.data)
        else:
            self.assertEqual(response.status_code, 200)


class TestDocumentParser(unittest.TestCase):
    """Test suite for document parsing functionality"""
    
    def setUp(self):
        self.parser = DocumentParser()
        self.test_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_allowed_file(self):
        """Test file extension validation"""
        self.assertTrue(self.parser.allowed_file('test.pdf'))
        self.assertTrue(self.parser.allowed_file('test.docx'))
        self.assertTrue(self.parser.allowed_file('test.txt'))
        self.assertTrue(self.parser.allowed_file('test.png'))
        self.assertFalse(self.parser.allowed_file('test.exe'))
        self.assertFalse(self.parser.allowed_file('test'))
    
    def test_get_file_type(self):
        """Test file type detection"""
        self.assertEqual(self.parser.get_file_type('test.pdf'), 'pdf')
        self.assertEqual(self.parser.get_file_type('test.docx'), 'word')
        self.assertEqual(self.parser.get_file_type('test.txt'), 'text')
        self.assertEqual(self.parser.get_file_type('test.png'), 'image')
        self.assertEqual(self.parser.get_file_type('test.unknown'), 'unknown')
    
    def test_extract_text_from_txt(self):
        """Test text extraction from TXT files"""
        test_content = "This is a test training document.\nIt contains multiple lines."
        test_file = os.path.join(self.test_dir, 'test.txt')
        
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        extracted = self.parser.extract_text_from_txt(test_file)
        self.assertEqual(extracted, test_content)
    
    def test_process_single_file(self):
        """Test processing a single file"""
        test_content = "Training: Python Basics\nDate: 2024-01-15"
        test_file = os.path.join(self.test_dir, 'test.txt')
        
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        file_info = {
            'filename': 'test.txt',
            'filepath': test_file,
            'type': 'text'
        }
        
        result = self.parser.process_single_file(file_info)
        
        self.assertEqual(result['filename'], 'test.txt')
        self.assertEqual(result['type'], 'text')
        self.assertEqual(result['content'], test_content)
        self.assertIn('size', result['metadata'])


class TestGeminiService(unittest.TestCase):
    """Test suite for Gemini AI service"""
    
    def setUp(self):
        # Skip tests if no API key is available
        if not os.environ.get('GEMINI_API_KEY'):
            self.skipTest("GEMINI_API_KEY not available")
        
        self.service = GeminiService()
    
    def test_template_structure(self):
        """Test that the report template has required fields"""
        template = self.service.report_template
        
        self.assertIn('training_details', template)
        self.assertIn('rationale', template)
        self.assertIn('objectives', template)
        self.assertIn('topics_covered', template)
        self.assertIn('issues_and_concerns', template)
        self.assertIn('recommendations', template)
        self.assertIn('plans_and_action_items', template)
        
        # Check training details structure
        training_details = template['training_details']
        self.assertIn('course_title', training_details)
        self.assertIn('course_code', training_details)
        self.assertIn('date', training_details)
        self.assertIn('venue', training_details)
    
    def test_merge_with_template(self):
        """Test merging data with template"""
        test_data = {
            'training_details': {
                'course_title': 'Test Course'
            },
            'rationale': 'Test rationale'
        }
        
        result = self.service._merge_with_template(test_data)
        
        # Should have all template fields
        self.assertIn('training_details', result)
        self.assertIn('objectives', result)
        
        # Should have merged data
        self.assertEqual(result['training_details']['course_title'], 'Test Course')
        self.assertEqual(result['rationale'], 'Test rationale')
        
        # Should have default values for unspecified fields
        self.assertEqual(result['training_details']['target_participants'], 'DICT Personnel')


class TestReportGeneratorClass(unittest.TestCase):
    """Test suite for report generation functionality"""
    
    def setUp(self):
        self.generator = ReportGenerator()
        self.test_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_custom_styles_setup(self):
        """Test that custom styles are properly set up"""
        self.assertIn('ReportHeader', self.generator.styles)
        self.assertIn('SectionHeader', self.generator.styles)
        self.assertIn('TableCell', self.generator.styles)
        self.assertIn('Signature', self.generator.styles)
    
    def test_generate_html_report(self):
        """Test HTML report generation"""
        test_data = {
            'training_details': {
                'course_title': 'Test Training Course',
                'course_code': 'TEST001',
                'date': '2024-01-15',
                'time': '9:00 AM - 5:00 PM',
                'duration': '8 hours',
                'venue': 'Test Venue',
                'resource_person': 'John Doe',
                'platform_used': 'Zoom',
                'mode': 'Online',
                'target_participants': 'DICT Personnel',
                'total_attendees': '25',
                'attendees_male': '15',
                'attendees_female': '10',
                'sector_categories': {
                    'nga': {'male': '5', 'female': '3'},
                    'lgu': {'male': '10', 'female': '7'},
                    'suc': {'male': '0', 'female': '0'},
                    'others': {'male': '0', 'female': '0'}
                },
                'total_certificates': {'male': '15', 'female': '10'}
            },
            'rationale': 'Test rationale for the training',
            'objectives': 'Test objectives',
            'topics_covered': 'Test topics covered',
            'issues_and_concerns': 'Test issues',
            'recommendations': 'Test recommendations',
            'plans_and_action_items': 'Test action items',
            'prepared_by': {
                'name': 'JAYKEE M.ABA-A',
                'position': 'Project Development Officer I'
            },
            'noted_by': {
                'name': 'ENGR. ELIZALDE S. RAMOS',
                'position': 'ITO I - Provincial Officer, SDS'
            }
        }
        
        html_content = self.generator._generate_html_report(test_data)
        
        # Check that HTML contains expected content
        self.assertIn('AFTER TRAINING REPORT', html_content)
        self.assertIn('Test Training Course', html_content)
        self.assertIn('TEST001', html_content)
        self.assertIn('Test rationale for the training', html_content)
        self.assertIn('JAYKEE M.ABA-A', html_content)


def run_tests():
    """Run all tests"""
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestReportGenerator,
        TestDocumentParser,
        TestGeminiService,
        TestReportGeneratorClass
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return result.wasSuccessful()


if __name__ == '__main__':
    success = run_tests()
    exit(0 if success else 1)

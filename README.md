# After Training Report Generator

An automated system for generating standardized After Training Reports from uploaded documents using Python/Flask and Google Gemini AI.

## Features

- **Multi-format File Upload**: Supports PDF, DOC, DOCX, TXT, and image files
- **AI-Powered Content Extraction**: Uses Google Gemini to intelligently extract training information
- **Template-Based Report Generation**: Generates reports matching the exact DICT template format
- **Interactive Preview & Editing**: Review and edit extracted data before final report generation
- **Professional PDF Output**: Maintains exact formatting of the official template

## System Requirements

- Python 3.8 or higher
- Google Gemini API key
- Modern web browser

## Installation

1. **Clone or download the project**
   ```bash
   git clone <repository-url>
   cd report_generator
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up Google Gemini API**
   - Get your API key from [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Set the environment variable:
   ```bash
   export GEMINI_API_KEY="your-api-key-here"
   ```

4. **Run the application**
   ```bash
   python run.py
   ```

5. **Access the application**
   - Open your browser to `http://localhost:5000`

## Usage

### Step 1: Upload Documents
- Navigate to the main page
- Upload training-related documents:
  - Training materials/curriculum
  - Evaluation forms and results
  - Attendance lists
  - Activity photos
  - Feedback documents

### Step 2: Review Extracted Data
- The system will automatically extract information using AI
- Review the populated template fields
- Edit any information as needed
- All sections can be modified before final generation

### Step 3: Generate Report
- Click "Generate Final Report" to create the PDF
- The report will match the exact DICT template format
- Download the completed report

## Supported File Types

- **Documents**: PDF, DOC, DOCX, TXT, RTF
- **Images**: PNG, JPG, JPEG, GIF, BMP, TIFF

## Template Structure

The generated report includes all sections from the official DICT After Training Report template:

1. **Training Details** - Course information, dates, participants
2. **Rationale** - Purpose and justification
3. **Objectives** - Training goals and outcomes
4. **Topics Covered** - Content and modules
5. **Issues and Concerns** - Identified challenges
6. **Recommendations** - Improvement suggestions
7. **Plans and Action Items** - Next steps
8. **Photo Documentation** - Visual records
9. **Signatures** - Prepared by and Noted by sections

## API Endpoints

- `GET /` - Main upload interface
- `POST /upload` - File upload and processing
- `GET /preview/<session_id>` - Preview extracted data
- `POST /update_data/<session_id>` - Update extracted data
- `GET /generate_report/<session_id>` - Generate final PDF report
- `GET /health` - Health check endpoint

## Configuration

Key configuration options in `config.py`:

- `UPLOAD_FOLDER` - Directory for uploaded files
- `REPORTS_FOLDER` - Directory for generated reports
- `MAX_CONTENT_LENGTH` - Maximum file upload size (50MB default)
- `GEMINI_API_KEY` - Google Gemini API key
- `GEMINI_MODEL` - AI model to use (gemini-1.5-pro default)

## Testing

Run the test suite:

```bash
python test_app.py
```

Tests cover:
- File upload functionality
- Document parsing
- AI content extraction
- Report generation
- Template formatting

## Project Structure

```
report_generator/
├── app.py                 # Main Flask application
├── config.py             # Configuration settings
├── requirements.txt      # Python dependencies
├── run.py               # Application runner
├── test_app.py          # Test suite
├── services/            # Core services
│   ├── __init__.py
│   ├── document_parser.py    # File parsing logic
│   ├── gemini_service.py     # AI integration
│   └── report_generator.py   # PDF generation
├── templates/           # HTML templates
│   ├── base.html
│   ├── index.html
│   └── preview.html
├── uploads/            # Uploaded files (created automatically)
└── reports/            # Generated reports (created automatically)
```

## Troubleshooting

### Common Issues

1. **"GEMINI_API_KEY not found"**
   - Ensure you've set the environment variable correctly
   - Verify your API key is valid

2. **File upload fails**
   - Check file size (max 50MB)
   - Ensure file type is supported
   - Verify sufficient disk space

3. **Report generation fails**
   - Check that all required fields are populated
   - Ensure output directory is writable
   - Verify WeasyPrint dependencies are installed

### Logs

Application logs are displayed in the console when running in debug mode. Check for error messages if issues occur.

## Security Considerations

- Files are stored temporarily and can be cleaned up regularly
- API keys should be kept secure and not committed to version control
- Consider implementing user authentication for production use
- Validate and sanitize all uploaded content

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is developed for the Department of Information and Communications Technology (DICT) of the Philippines.

## Support

For technical support or questions about the system, please contact the development team or refer to the project documentation.

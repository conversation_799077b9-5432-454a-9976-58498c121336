import google.generativeai as genai
import json
import logging
from typing import Dict, Any, List, Optional
from config import Config

logger = logging.getLogger(__name__)

class GeminiService:
    """Service for interacting with Google Gemini API for content analysis and structuring"""
    
    def __init__(self):
        self.api_key = Config.GEMINI_API_KEY
        if not self.api_key:
            raise ValueError("GEMINI_API_KEY environment variable is required")
        
        genai.configure(api_key=self.api_key)
        self.model = genai.GenerativeModel(Config.GEMINI_MODEL)
        
        # Template structure for the After Training Report
        self.report_template = {
            "training_details": {
                "course_title": "",
                "course_code": "",
                "date": "",
                "time": "",
                "duration": "",
                "venue": "",
                "resource_person": "",
                "platform_used": "",
                "mode": "",
                "target_participants": "DICT Personnel",
                "total_attendees": "",
                "attendees_male": "",
                "attendees_female": "",
                "sector_categories": {
                    "nga": {"male": "", "female": ""},
                    "lgu": {"male": "", "female": ""},
                    "suc": {"male": "", "female": ""},
                    "others": {"male": "", "female": ""}
                },
                "total_certificates": {"male": "", "female": ""}
            },
            "rationale": "",
            "objectives": "",
            "topics_covered": "",
            "issues_and_concerns": "",
            "recommendations": "",
            "plans_and_action_items": "",
            "photo_documentation": [],
            "prepared_by": {
                "name": "JAYKEE M.ABA-A",
                "position": "Project Development Officer I"
            },
            "noted_by": {
                "name": "ENGR. ELIZALDE S. RAMOS",
                "position": "ITO I - Provincial Officer, SDS"
            }
        }
    
    def structure_training_data(self, extracted_data: Dict[str, Any]) -> Dict[str, Any]:
        """Use Gemini to structure extracted data into report format"""
        try:
            # Combine all text content
            all_text = ""
            for text_file in extracted_data.get('text_content', []):
                all_text += f"\n--- {text_file['filename']} ---\n"
                all_text += text_file['content']
            
            if not all_text.strip():
                logger.warning("No text content found to process")
                return self.report_template
            
            # Create prompt for Gemini
            prompt = self._create_structuring_prompt(all_text)
            
            # Generate response
            response = self.model.generate_content(prompt)
            
            if not response.text:
                logger.error("Empty response from Gemini")
                return self.report_template
            
            # Parse JSON response
            try:
                structured_data = json.loads(response.text)
                # Merge with template to ensure all fields exist
                return self._merge_with_template(structured_data)
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse Gemini JSON response: {e}")
                # Try to extract data manually if JSON parsing fails
                return self._extract_data_manually(all_text)
                
        except Exception as e:
            logger.error(f"Error in structure_training_data: {str(e)}")
            return self.report_template
    
    def _create_structuring_prompt(self, text_content: str) -> str:
        """Create a detailed prompt for Gemini to structure the training data"""
        return f"""
You are an expert at analyzing training documents and extracting structured information for official reports.

Please analyze the following training-related documents and extract information to populate an "After Training Report" template.

DOCUMENTS TO ANALYZE:
{text_content}

INSTRUCTIONS:
1. Extract information that matches the following report structure
2. If specific information is not found, leave the field empty (don't make up data)
3. For numerical data (attendees, etc.), extract exact numbers if available
4. For dates, convert to standard format (YYYY-MM-DD)
5. For names and titles, extract exactly as written
6. Return ONLY valid JSON in the exact structure below

REQUIRED JSON STRUCTURE:
{{
    "training_details": {{
        "course_title": "extracted course/training title",
        "course_code": "extracted course code if available",
        "date": "training date in YYYY-MM-DD format",
        "time": "training time if specified",
        "duration": "training duration (e.g., '8 hours', '2 days')",
        "venue": "training venue/location",
        "resource_person": "trainer/facilitator name",
        "platform_used": "platform if online (Zoom, Teams, etc.)",
        "mode": "Online/Face-to-face/Hybrid",
        "target_participants": "target audience description",
        "total_attendees": "total number of attendees",
        "attendees_male": "number of male attendees",
        "attendees_female": "number of female attendees",
        "sector_categories": {{
            "nga": {{"male": "number", "female": "number"}},
            "lgu": {{"male": "number", "female": "number"}},
            "suc": {{"male": "number", "female": "number"}},
            "others": {{"male": "number", "female": "number"}}
        }},
        "total_certificates": {{"male": "number", "female": "number"}}
    }},
    "rationale": "purpose/justification for the training",
    "objectives": "training objectives or learning outcomes",
    "topics_covered": "list or description of topics/modules covered",
    "issues_and_concerns": "any issues or concerns identified",
    "recommendations": "recommendations for improvement",
    "plans_and_action_items": "next steps or action items"
}}

Return only the JSON structure with extracted data. Do not include any explanatory text.
"""
    
    def _merge_with_template(self, structured_data: Dict[str, Any]) -> Dict[str, Any]:
        """Merge structured data with template to ensure all fields exist"""
        result = self.report_template.copy()
        
        # Deep merge function
        def deep_merge(template, data):
            for key, value in data.items():
                if key in template:
                    if isinstance(template[key], dict) and isinstance(value, dict):
                        deep_merge(template[key], value)
                    else:
                        template[key] = value
        
        deep_merge(result, structured_data)
        return result
    
    def _extract_data_manually(self, text_content: str) -> Dict[str, Any]:
        """Fallback method to extract data manually if Gemini JSON parsing fails"""
        result = self.report_template.copy()
        
        # Simple keyword-based extraction as fallback
        text_lower = text_content.lower()
        
        # Try to extract basic information
        lines = text_content.split('\n')
        for line in lines:
            line_lower = line.lower().strip()
            if 'course' in line_lower or 'training' in line_lower:
                if len(line.strip()) < 100:  # Likely a title
                    result['training_details']['course_title'] = line.strip()
                    break
        
        # Extract rationale, objectives, etc. from sections
        current_section = None
        for line in lines:
            line_stripped = line.strip()
            if not line_stripped:
                continue
                
            line_lower = line_stripped.lower()
            
            if 'rationale' in line_lower:
                current_section = 'rationale'
            elif 'objective' in line_lower:
                current_section = 'objectives'
            elif 'topic' in line_lower and 'cover' in line_lower:
                current_section = 'topics_covered'
            elif 'issue' in line_lower or 'concern' in line_lower:
                current_section = 'issues_and_concerns'
            elif 'recommendation' in line_lower:
                current_section = 'recommendations'
            elif 'plan' in line_lower or 'action' in line_lower:
                current_section = 'plans_and_action_items'
            elif current_section and len(line_stripped) > 10:
                # Add content to current section
                if result[current_section]:
                    result[current_section] += '\n' + line_stripped
                else:
                    result[current_section] = line_stripped
        
        return result
    
    def enhance_extracted_data(self, data: Dict[str, Any], user_feedback: str = "") -> Dict[str, Any]:
        """Use Gemini to enhance or correct extracted data based on user feedback"""
        try:
            prompt = f"""
Please review and enhance the following training report data based on the user feedback provided.

CURRENT DATA:
{json.dumps(data, indent=2)}

USER FEEDBACK:
{user_feedback}

Please return the corrected/enhanced data in the same JSON structure. Only modify fields that need correction based on the feedback.
"""
            
            response = self.model.generate_content(prompt)
            
            if response.text:
                try:
                    enhanced_data = json.loads(response.text)
                    return self._merge_with_template(enhanced_data)
                except json.JSONDecodeError:
                    logger.error("Failed to parse enhanced data JSON")
                    return data
            
            return data
            
        except Exception as e:
            logger.error(f"Error enhancing data: {str(e)}")
            return data

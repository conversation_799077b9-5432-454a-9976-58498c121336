{% extends "base.html" %}

{% block title %}Upload Files - Report Generator{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="fas fa-upload me-2"></i>
                    Upload Training Documents
                </h4>
            </div>
            <div class="card-body">
                <p class="text-muted mb-4">
                    Upload your training-related documents to automatically generate an After Training Report. 
                    Supported formats: PDF, DOC, DOCX, TXT, and images (PNG, JPG, etc.).
                </p>

                <form id="uploadForm" enctype="multipart/form-data">
                    <div class="upload-area" id="uploadArea">
                        <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                        <h5>Drag and drop files here</h5>
                        <p class="text-muted">or click to select files</p>
                        <input type="file" id="fileInput" name="files" multiple 
                               accept=".pdf,.doc,.docx,.txt,.rtf,.png,.jpg,.jpeg,.gif,.bmp,.tiff"
                               class="d-none">
                        <button type="button" class="btn btn-outline-primary" onclick="document.getElementById('fileInput').click()">
                            <i class="fas fa-folder-open me-2"></i>
                            Select Files
                        </button>
                    </div>

                    <div id="fileList" class="file-list mt-3" style="display: none;">
                        <h6>Selected Files:</h6>
                        <div id="fileItems"></div>
                    </div>

                    <div class="progress-container mt-3" id="progressContainer">
                        <div class="progress">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                 role="progressbar" style="width: 0%"></div>
                        </div>
                        <small class="text-muted mt-2 d-block">Processing files...</small>
                    </div>

                    <div class="mt-4">
                        <button type="submit" class="btn btn-primary btn-lg" id="uploadBtn" disabled>
                            <i class="fas fa-magic me-2"></i>
                            Generate Report
                        </button>
                        <button type="button" class="btn btn-secondary btn-lg ms-2" id="clearBtn" onclick="clearFiles()">
                            <i class="fas fa-trash me-2"></i>
                            Clear Files
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Instructions
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Supported Document Types:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-file-pdf text-danger me-2"></i>PDF Documents</li>
                            <li><i class="fas fa-file-word text-primary me-2"></i>Word Documents (.doc, .docx)</li>
                            <li><i class="fas fa-file-alt text-secondary me-2"></i>Text Files (.txt, .rtf)</li>
                            <li><i class="fas fa-image text-success me-2"></i>Images (PNG, JPG, etc.)</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>What to Upload:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>Training materials/curriculum</li>
                            <li><i class="fas fa-check text-success me-2"></i>Evaluation forms and results</li>
                            <li><i class="fas fa-check text-success me-2"></i>Attendance lists</li>
                            <li><i class="fas fa-check text-success me-2"></i>Activity photos</li>
                            <li><i class="fas fa-check text-success me-2"></i>Feedback documents</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let selectedFiles = [];

// File input change handler
document.getElementById('fileInput').addEventListener('change', function(e) {
    handleFiles(e.target.files);
});

// Drag and drop handlers
const uploadArea = document.getElementById('uploadArea');

uploadArea.addEventListener('dragover', function(e) {
    e.preventDefault();
    uploadArea.classList.add('dragover');
});

uploadArea.addEventListener('dragleave', function(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
});

uploadArea.addEventListener('drop', function(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
    handleFiles(e.dataTransfer.files);
});

// Click handler for upload area
uploadArea.addEventListener('click', function(e) {
    if (e.target === uploadArea || e.target.closest('.upload-area')) {
        document.getElementById('fileInput').click();
    }
});

function handleFiles(files) {
    selectedFiles = Array.from(files);
    displayFiles();
    document.getElementById('uploadBtn').disabled = selectedFiles.length === 0;
}

function displayFiles() {
    const fileList = document.getElementById('fileList');
    const fileItems = document.getElementById('fileItems');
    
    if (selectedFiles.length === 0) {
        fileList.style.display = 'none';
        return;
    }
    
    fileList.style.display = 'block';
    fileItems.innerHTML = '';
    
    selectedFiles.forEach((file, index) => {
        const fileItem = document.createElement('div');
        fileItem.className = 'file-item';
        
        const icon = getFileIcon(file.name);
        const size = formatFileSize(file.size);
        
        fileItem.innerHTML = `
            <i class="${icon} file-icon"></i>
            <div class="flex-grow-1">
                <div class="fw-bold">${file.name}</div>
                <small class="text-muted">${size}</small>
            </div>
            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeFile(${index})">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        fileItems.appendChild(fileItem);
    });
}

function getFileIcon(filename) {
    const ext = filename.split('.').pop().toLowerCase();
    switch (ext) {
        case 'pdf': return 'fas fa-file-pdf text-danger';
        case 'doc':
        case 'docx': return 'fas fa-file-word text-primary';
        case 'txt':
        case 'rtf': return 'fas fa-file-alt text-secondary';
        case 'png':
        case 'jpg':
        case 'jpeg':
        case 'gif':
        case 'bmp':
        case 'tiff': return 'fas fa-image text-success';
        default: return 'fas fa-file text-muted';
    }
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function removeFile(index) {
    selectedFiles.splice(index, 1);
    displayFiles();
    document.getElementById('uploadBtn').disabled = selectedFiles.length === 0;
}

function clearFiles() {
    selectedFiles = [];
    document.getElementById('fileInput').value = '';
    displayFiles();
    document.getElementById('uploadBtn').disabled = true;
}

// Form submission
document.getElementById('uploadForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    if (selectedFiles.length === 0) {
        alert('Please select files to upload.');
        return;
    }
    
    const formData = new FormData();
    selectedFiles.forEach(file => {
        formData.append('files', file);
    });
    
    // Show progress
    const progressContainer = document.getElementById('progressContainer');
    const uploadBtn = document.getElementById('uploadBtn');
    
    progressContainer.style.display = 'block';
    uploadBtn.disabled = true;
    uploadBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Processing...';
    
    fetch('/upload', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.location.href = `/preview/${data.session_id}`;
        } else {
            throw new Error(data.error || 'Upload failed');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error: ' + error.message);
        
        // Reset UI
        progressContainer.style.display = 'none';
        uploadBtn.disabled = false;
        uploadBtn.innerHTML = '<i class="fas fa-magic me-2"></i>Generate Report';
    });
});
</script>
{% endblock %}
